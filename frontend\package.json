{"name": "widdx-ai-frontend", "version": "2.0.0", "description": "WIDDX AI - Next-Generation Intelligent Assistant Frontend", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.ts --fix", "format": "prettier --write .", "test": "vitest", "test:ui": "vitest --ui", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.15", "vue-router": "^4.2.5", "pinia": "^2.1.7", "vue-i18n": "^9.8.0", "@vueuse/core": "^10.7.2", "axios": "^1.6.7", "socket.io-client": "^4.7.4", "tailwindcss": "^3.4.1", "@headlessui/vue": "^1.7.17", "@heroicons/vue": "^2.0.18", "floating-vue": "^5.2.2", "vue-toastification": "^2.0.0-rc.5", "chart.js": "^4.4.1", "vue-chartjs": "^5.3.0", "marked": "^11.1.1", "highlight.js": "^11.9.0", "file-saver": "^2.0.5", "date-fns": "^3.3.1", "lodash-es": "^4.17.21", "uuid": "^9.0.1", "mitt": "^3.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.3", "@vue/tsconfig": "^0.5.1", "vite": "^5.0.12", "typescript": "^5.3.3", "vue-tsc": "^1.8.27", "autoprefixer": "^10.4.17", "postcss": "^8.4.33", "@types/node": "^20.11.16", "@types/lodash-es": "^4.17.12", "@types/uuid": "^9.0.8", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "eslint-plugin-vue": "^9.20.1", "prettier": "^3.2.5", "vitest": "^1.2.2", "@vue/test-utils": "^2.4.4", "jsdom": "^24.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}