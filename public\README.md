# WIDDX AI Frontend - Bootstrap + OOP Architecture

## 🚀 Overview

This is the modern frontend for WIDDX AI, built with **Bootstrap 5**, **Object-Oriented JavaScript**, and **Glassmorphism design**. The frontend provides a comprehensive chat interface with multi-language support, real-time communication, and advanced AI features.

## 🏗️ Architecture

### **Object-Oriented Design**
- **Core Classes**: EventEmitter, ApiClient, StorageManager, I18nManager, ThemeManager
- **Service Classes**: AuthService, ChatService, PersonalityService, KnowledgeService, FileService, RealtimeService
- **UI Components**: MessageRenderer, ConversationList, ChatInterface, ToastManager
- **Main Application**: WiddxApp (orchestrates all services and components)

### **Technology Stack**
- **Frontend Framework**: Vanilla JavaScript with OOP patterns
- **UI Framework**: Bootstrap 5.3.2 with custom glassmorphism styling
- **Icons**: Bootstrap Icons 1.11.2
- **Syntax Highlighting**: Highlight.js 11.9.0
- **Markdown Parsing**: Marked.js 11.1.1
- **Real-time Communication**: Socket.IO 4.7.4
- **Build Tools**: No build process required - pure ES6+ modules

## 📁 Project Structure

```
public/
├── index.html                 # Main HTML file
├── assets/
│   ├── css/
│   │   └── main.css          # Custom styles with glassmorphism
│   └── js/
│       ├── core/             # Core system classes
│       │   ├── EventEmitter.js
│       │   ├── ApiClient.js
│       │   ├── StorageManager.js
│       │   ├── I18nManager.js
│       │   └── ThemeManager.js
│       ├── services/         # Business logic services
│       │   ├── AuthService.js
│       │   ├── ChatService.js
│       │   ├── PersonalityService.js
│       │   ├── KnowledgeService.js
│       │   ├── FileService.js
│       │   └── RealtimeService.js
│       ├── components/       # UI components
│       │   ├── MessageRenderer.js
│       │   ├── ConversationList.js
│       │   ├── ChatInterface.js
│       │   └── ToastManager.js
│       └── WiddxApp.js       # Main application class
└── README.md                 # This file
```

## 🎨 Design Features

### **Glassmorphism UI**
- Translucent backgrounds with backdrop blur
- Subtle borders and shadows
- Modern glass-effect styling
- Smooth animations and transitions

### **Dark/Light Theme Support**
- Automatic system theme detection
- Manual theme switching
- Persistent theme preferences
- Smooth theme transitions

### **Multi-Language Support**
- English, Arabic, French, Spanish, German
- RTL/LTR text direction support
- Dynamic language switching
- Localized date/time formatting

### **Responsive Design**
- Mobile-first approach
- Adaptive layouts for all screen sizes
- Touch-friendly interactions
- Progressive enhancement

## 🔧 Core Classes

### **EventEmitter**
Base class providing event-driven architecture:
```javascript
const emitter = new EventEmitter();
emitter.on('event', handler);
emitter.emit('event', data);
```

### **ApiClient**
HTTP client with authentication and error handling:
```javascript
const api = new ApiClient('/api');
api.setAuthToken(token);
const response = await api.get('/endpoint');
```

### **StorageManager**
Unified storage management (localStorage, sessionStorage, IndexedDB):
```javascript
const storage = new StorageManager();
storage.setLocal('key', value, { encrypt: true });
const value = storage.getLocal('key');
```

### **I18nManager**
Internationalization with RTL support:
```javascript
const i18n = new I18nManager();
await i18n.setLanguage('ar');
const text = i18n.t('chat.welcomeMessage');
```

### **ThemeManager**
Theme management with system preference detection:
```javascript
const theme = new ThemeManager();
theme.setTheme('dark');
theme.toggleTheme();
```

## 🔌 Service Classes

### **AuthService**
User authentication and session management:
```javascript
const auth = new AuthService(apiClient, storage);
await auth.login(email, password);
const user = auth.getUser();
```

### **ChatService**
Chat and conversation management:
```javascript
const chat = new ChatService(apiClient, storage);
const conversation = await chat.createConversation();
await chat.sendMessage(conversationId, message);
```

### **PersonalityService**
AI personality customization:
```javascript
const personality = new PersonalityService(apiClient, storage);
await personality.switchPersonality(personalityId);
await personality.customizePersonality(traits);
```

### **KnowledgeService**
Knowledge base management:
```javascript
const knowledge = new KnowledgeService(apiClient, storage);
const results = await knowledge.searchKnowledge(query);
await knowledge.createKnowledgeEntry(data);
```

### **FileService**
File upload and processing:
```javascript
const files = new FileService(apiClient, storage);
const result = await files.uploadFile(formData);
await files.processFile(fileId);
```

### **RealtimeService**
WebSocket real-time communication:
```javascript
const realtime = new RealtimeService(authToken);
realtime.joinRoom(conversationId);
realtime.sendTyping(conversationId);
```

## 🎯 UI Components

### **MessageRenderer**
Renders chat messages with markdown and syntax highlighting:
```javascript
const renderer = new MessageRenderer(i18n);
const element = renderer.renderMessage(message);
```

### **ConversationList**
Manages conversation sidebar:
```javascript
const list = new ConversationList(chatService, i18n);
list.addConversation(conversation);
```

### **ChatInterface**
Main chat interface with file upload:
```javascript
const chat = new ChatInterface(chatService, fileService, renderer, i18n);
await chat.sendMessage();
```

### **ToastManager**
Notification system:
```javascript
const toast = new ToastManager();
toast.showSuccess('Message sent!');
toast.showError('Connection failed');
```

## 🚀 Getting Started

### **1. Setup**
```bash
# No build process required - just serve the files
# Using Python
python -m http.server 8080

# Using Node.js
npx serve .

# Using PHP
php -S localhost:8080
```

### **2. Configuration**
Update the API base URL in `WiddxApp.js`:
```javascript
this.apiClient = new ApiClient('/api'); // Change to your backend URL
```

### **3. Initialization**
The application auto-initializes when the DOM is ready:
```javascript
document.addEventListener('DOMContentLoaded', () => {
    window.widdxApp = new WiddxApp();
});
```

## 🔐 Authentication Flow

1. **Login**: User enters credentials
2. **Token Storage**: JWT token stored securely
3. **Auto-Refresh**: Token refreshed before expiration
4. **Session Restore**: Automatic login on page reload
5. **Logout**: Clean session termination

## 💬 Chat Features

### **Real-time Messaging**
- WebSocket connection for instant updates
- Typing indicators
- Message status (sending, sent, failed)
- Auto-reconnection with exponential backoff

### **Message Types**
- Text messages with markdown support
- File attachments (images, documents, audio)
- Code blocks with syntax highlighting
- Chain-of-thought reasoning display

### **Conversation Management**
- Create, rename, delete conversations
- Search conversation history
- Export conversations (JSON, PDF, TXT)
- Conversation list with previews

## 🧠 AI Features

### **Personality System**
- Multiple AI personalities
- Custom personality traits
- User memory management
- Personality switching

### **Knowledge Base**
- Search knowledge entries
- Add custom knowledge
- File processing and extraction
- Knowledge optimization

### **Multi-Modal Support**
- Image analysis
- Document processing
- Audio transcription
- Code analysis

## 🌍 Internationalization

### **Supported Languages**
- **English** (en) - Default
- **Arabic** (ar) - RTL support
- **French** (fr)
- **Spanish** (es)
- **German** (de)

### **Adding New Languages**
1. Add translations to `I18nManager.js`
2. Update language selector in HTML
3. Test RTL layout if applicable

## 🎨 Customization

### **Themes**
Modify theme colors in `ThemeManager.js`:
```javascript
themes: {
    custom: {
        name: 'Custom',
        icon: 'bi-palette',
        colors: {
            primary: '#your-color',
            background: '#your-bg'
        }
    }
}
```

### **Styling**
Update glassmorphism effects in `main.css`:
```css
.glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
```

## 📱 Mobile Support

- **Responsive Design**: Adapts to all screen sizes
- **Touch Gestures**: Swipe navigation
- **Mobile Optimizations**: Optimized for mobile browsers
- **PWA Ready**: Can be installed as a web app

## 🔧 Development

### **Adding New Features**
1. Create service class in `services/`
2. Add UI component in `components/`
3. Integrate with `WiddxApp.js`
4. Update translations in `I18nManager.js`

### **Event System**
All components use the EventEmitter pattern:
```javascript
// Emit events
this.emit('event:name', data);

// Listen to events
service.on('event:name', handler);
```

### **Error Handling**
Global error handling in `WiddxApp.js`:
```javascript
handleApiError(error) {
    if (error.status === 401) {
        // Handle authentication error
    }
    // Show user-friendly message
}
```

## 🧪 Testing

### **Manual Testing**
1. Test all chat features
2. Verify theme switching
3. Test language switching
4. Check mobile responsiveness
5. Test file uploads
6. Verify real-time features

### **Browser Compatibility**
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

## 🚀 Deployment

### **Production Build**
1. Minify CSS and JavaScript files
2. Optimize images and assets
3. Configure CDN for static assets
4. Set up proper caching headers

### **Environment Configuration**
Update API endpoints for different environments:
```javascript
const API_BASE = {
    development: 'http://localhost:8000/api',
    staging: 'https://staging-api.widdx.ai/api',
    production: 'https://api.widdx.ai/api'
}[process.env.NODE_ENV || 'development'];
```

## 📊 Performance

### **Optimization Features**
- **Lazy Loading**: Components loaded on demand
- **Caching**: Intelligent caching of API responses
- **Compression**: Gzip compression for assets
- **CDN**: Static assets served from CDN

### **Memory Management**
- **Event Cleanup**: Automatic event listener cleanup
- **Cache Limits**: Configurable cache size limits
- **Garbage Collection**: Proper object disposal

## 🔒 Security

### **Security Features**
- **XSS Protection**: HTML escaping and sanitization
- **CSRF Protection**: CSRF tokens for state-changing operations
- **Secure Storage**: Encrypted storage for sensitive data
- **Content Security Policy**: CSP headers for additional protection

## 📚 API Integration

The frontend integrates with the Laravel backend through RESTful APIs:

### **Authentication Endpoints**
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Token refresh
- `GET /v2/auth/verify` - Token verification

### **Chat Endpoints**
- `GET /v2/conversations` - List conversations
- `POST /v2/conversations` - Create conversation
- `GET /v2/conversations/{id}/messages` - Get messages
- `POST /v2/conversations/{id}/messages` - Send message

### **File Endpoints**
- `POST /v2/files/upload` - Upload file
- `GET /v2/files/{id}` - Get file details
- `POST /v2/files/{id}/process` - Process file

## 🤝 Contributing

1. Follow the OOP architecture patterns
2. Use the EventEmitter for component communication
3. Add proper error handling
4. Update translations for new features
5. Test on multiple browsers and devices

## 📄 License

This project is part of the WIDDX AI system. All rights reserved.

---

**WIDDX AI Frontend** - Next-Generation Intelligent Assistant Interface
