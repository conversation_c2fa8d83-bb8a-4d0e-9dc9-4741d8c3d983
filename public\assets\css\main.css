/* WIDDX AI - Main Stylesheet */
/* Bootstrap 5 + Custom Glassmorphism Design */

:root {
  /* Primary Colors */
  --widdx-primary: #0ea5e9;
  --widdx-primary-dark: #0284c7;
  --widdx-secondary: #d946ef;
  --widdx-secondary-dark: #c026d3;
  
  /* Glassmorphism Colors */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: rgba(0, 0, 0, 0.1);
  
  /* Dark Theme Colors */
  --dark-bg-primary: #0f172a;
  --dark-bg-secondary: #1e293b;
  --dark-bg-tertiary: #334155;
  --dark-text-primary: #f8fafc;
  --dark-text-secondary: #cbd5e1;
  --dark-text-muted: #64748b;
  
  /* Animation Variables */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Global Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, var(--dark-bg-primary) 0%, var(--dark-bg-secondary) 100%);
  min-height: 100vh;
  overflow-x: hidden;
}

/* Arabic Font Support */
[dir="rtl"] {
  font-family: 'Tajawal', 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Glassmorphism Effects */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  box-shadow: 0 8px 32px var(--glass-shadow);
}

.glass-button {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  transition: all var(--transition-fast);
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--dark-bg-secondary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--dark-bg-tertiary);
  border-radius: 4px;
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--widdx-primary);
}

/* Navigation Enhancements */
.navbar {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(15, 23, 42, 0.9) !important;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

/* Sidebar Styling */
#sidebar {
  background: linear-gradient(180deg, var(--dark-bg-primary) 0%, var(--dark-bg-secondary) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* Conversation Items */
.conversation-item {
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 12px;
  cursor: pointer;
  transition: all var(--transition-fast);
  border: 1px solid transparent;
}

.conversation-item:hover {
  background: var(--glass-bg);
  border-color: var(--glass-border);
  transform: translateX(4px);
}

.conversation-item.active {
  background: var(--widdx-primary);
  color: white;
  box-shadow: 0 4px 16px rgba(14, 165, 233, 0.3);
}

.conversation-item .conversation-title {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.conversation-item .conversation-preview {
  font-size: 0.8rem;
  opacity: 0.7;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Message Bubbles */
.message-bubble {
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message .message-content {
  background: var(--widdx-primary);
  color: white;
  border-radius: 18px 18px 4px 18px;
  padding: 12px 16px;
  max-width: 80%;
  margin-left: auto;
  word-wrap: break-word;
}

.ai-message .message-content {
  background: var(--dark-bg-secondary);
  color: var(--dark-text-primary);
  border-radius: 18px 18px 18px 4px;
  padding: 12px 16px;
  max-width: 85%;
  word-wrap: break-word;
  border: 1px solid var(--dark-bg-tertiary);
}

/* Avatar Styling */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  flex-shrink: 0;
}

.user-avatar {
  background: var(--widdx-secondary);
}

.ai-avatar {
  background: var(--widdx-primary);
}

/* Message Actions */
.message-actions {
  opacity: 0;
  transition: opacity var(--transition-fast);
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.message-bubble:hover .message-actions {
  opacity: 1;
}

.message-action-btn {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--dark-text-secondary);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  transition: all var(--transition-fast);
}

.message-action-btn:hover {
  background: var(--widdx-primary);
  color: white;
  transform: scale(1.05);
}

/* Reasoning Steps */
.reasoning-steps {
  background: var(--dark-bg-tertiary);
  border-radius: 12px;
  padding: 16px;
  margin-top: 12px;
  border-left: 4px solid var(--widdx-primary);
}

.reasoning-step {
  padding: 8px 0;
  border-bottom: 1px solid var(--dark-bg-secondary);
}

.reasoning-step:last-child {
  border-bottom: none;
}

.reasoning-step-title {
  font-weight: 600;
  color: var(--widdx-primary);
  font-size: 0.9rem;
}

.reasoning-step-content {
  font-size: 0.85rem;
  color: var(--dark-text-secondary);
  margin-top: 4px;
}

/* Code Highlighting */
pre {
  background: var(--dark-bg-primary) !important;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  border: 1px solid var(--dark-bg-tertiary);
}

code {
  background: var(--dark-bg-tertiary);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.9em;
}

/* Input Styling */
.form-control {
  background: var(--dark-bg-secondary);
  border: 1px solid var(--dark-bg-tertiary);
  color: var(--dark-text-primary);
  transition: all var(--transition-fast);
}

.form-control:focus {
  background: var(--dark-bg-secondary);
  border-color: var(--widdx-primary);
  box-shadow: 0 0 0 0.2rem rgba(14, 165, 233, 0.25);
  color: var(--dark-text-primary);
}

.form-control::placeholder {
  color: var(--dark-text-muted);
}

/* Button Enhancements */
.btn-primary {
  background: var(--widdx-primary);
  border-color: var(--widdx-primary);
  transition: all var(--transition-fast);
}

.btn-primary:hover {
  background: var(--widdx-primary-dark);
  border-color: var(--widdx-primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(14, 165, 233, 0.3);
}

.btn-outline-primary {
  color: var(--widdx-primary);
  border-color: var(--widdx-primary);
}

.btn-outline-primary:hover {
  background: var(--widdx-primary);
  border-color: var(--widdx-primary);
  transform: translateY(-1px);
}

/* File Upload Area */
.file-upload-area {
  border: 2px dashed var(--dark-bg-tertiary);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  transition: all var(--transition-fast);
  cursor: pointer;
}

.file-upload-area:hover {
  border-color: var(--widdx-primary);
  background: var(--glass-bg);
}

.file-upload-area.dragover {
  border-color: var(--widdx-primary);
  background: rgba(14, 165, 233, 0.1);
}

/* File Preview */
.file-preview {
  background: var(--dark-bg-secondary);
  border: 1px solid var(--dark-bg-tertiary);
  border-radius: 8px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.file-preview .file-icon {
  color: var(--widdx-primary);
}

.file-preview .remove-file {
  color: var(--dark-text-muted);
  cursor: pointer;
  transition: color var(--transition-fast);
}

.file-preview .remove-file:hover {
  color: #dc3545;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background: var(--dark-bg-secondary);
  border-radius: 18px;
  margin-bottom: 16px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--widdx-primary);
  animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingBounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Toast Styling */
.toast {
  background: var(--dark-bg-secondary);
  border: 1px solid var(--dark-bg-tertiary);
  color: var(--dark-text-primary);
}

.toast-success {
  border-left: 4px solid #198754;
}

.toast-error {
  border-left: 4px solid #dc3545;
}

.toast-warning {
  border-left: 4px solid #ffc107;
}

.toast-info {
  border-left: 4px solid var(--widdx-primary);
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, var(--dark-bg-secondary) 25%, var(--dark-bg-tertiary) 50%, var(--dark-bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Design */
@media (max-width: 991.98px) {
  #sidebar {
    position: fixed;
    top: 56px;
    left: -100%;
    width: 280px;
    height: calc(100vh - 56px);
    z-index: 1000;
    transition: left var(--transition-normal);
  }
  
  #sidebar.show {
    left: 0;
  }
  
  .sidebar-overlay {
    position: fixed;
    top: 56px;
    left: 0;
    width: 100%;
    height: calc(100vh - 56px);
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
  }
  
  .sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
  }
}

/* Light Theme Support */
[data-bs-theme="light"] {
  --dark-bg-primary: #ffffff;
  --dark-bg-secondary: #f8f9fa;
  --dark-bg-tertiary: #e9ecef;
  --dark-text-primary: #212529;
  --dark-text-secondary: #495057;
  --dark-text-muted: #6c757d;
  --glass-bg: rgba(0, 0, 0, 0.05);
  --glass-border: rgba(0, 0, 0, 0.1);
  --glass-shadow: rgba(0, 0, 0, 0.05);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Indicators */
.btn:focus,
.form-control:focus {
  outline: 2px solid var(--widdx-primary);
  outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .message-bubble {
    border: 2px solid currentColor;
  }
  
  .btn {
    border-width: 2px;
  }
}
