/**
 * WiddxApp - Main application class for WIDDX AI
 * Orchestrates all services and manages application lifecycle
 */
class WiddxApp extends EventEmitter {
    constructor() {
        super();
        this.version = '2.0.0';
        this.isInitialized = false;
        this.isOnline = navigator.onLine;

        // Core services
        this.apiClient = null;
        this.storage = null;
        this.i18n = null;
        this.auth = null;
        this.chat = null;
        this.personality = null;
        this.knowledge = null;
        this.files = null;
        this.realtime = null;

        // UI components
        this.messageRenderer = null;
        this.conversationList = null;
        this.chatInterface = null;
        this.toastManager = null;
        this.themeManager = null;

        // Application state
        this.currentView = 'chat';
        this.isLoading = false;
        this.errorState = null;

        this.initialize();
    }

    /**
     * Initialize the application
     */
    async initialize() {
        try {
            this.showLoadingScreen();
            this.emit('app:initializing');

            // Initialize core services
            await this.initializeCoreServices();

            // Initialize UI components
            await this.initializeUIComponents();

            // Set up event listeners
            this.setupEventListeners();

            // Initialize routing
            this.initializeRouting();

            // Check authentication status
            await this.checkAuthenticationStatus();

            // Hide loading screen and show app
            this.hideLoadingScreen();

            this.isInitialized = true;
            this.emit('app:initialized');

            console.log(`WIDDX AI v${this.version} initialized successfully`);

        } catch (error) {
            console.error('Application initialization failed:', error);
            this.handleInitializationError(error);
        }
    }

    /**
     * Initialize core services
     */
    async initializeCoreServices() {
        // Initialize API client
        this.apiClient = new ApiClient('/api');

        // Initialize storage manager
        this.storage = new StorageManager();
        await this.storage.waitFor('storage:db-ready', 5000).catch(() => {
            console.warn('IndexedDB initialization timeout, continuing with localStorage');
        });

        // Initialize internationalization
        this.i18n = new I18nManager();
        await this.i18n.waitFor('i18n:initialized', 3000);

        // Initialize theme manager (simple implementation)
        this.themeManager = {
            toggleTheme: () => {
                const html = document.documentElement;
                const currentTheme = html.getAttribute('data-bs-theme');
                const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
                html.setAttribute('data-bs-theme', newTheme);
                this.storage.setLocal('theme', newTheme);

                // Update theme toggle icon
                const themeToggle = document.getElementById('theme-toggle');
                if (themeToggle) {
                    const icon = themeToggle.querySelector('i');
                    if (icon) {
                        icon.className = newTheme === 'dark' ? 'bi bi-sun-fill' : 'bi bi-moon-fill';
                    }
                }
            },

            initializeTheme: () => {
                const savedTheme = this.storage.getLocal('theme', 'dark');
                document.documentElement.setAttribute('data-bs-theme', savedTheme);
            }
        };

        this.themeManager.initializeTheme();

        // Initialize authentication service
        this.auth = new AuthService(this.apiClient, this.storage);
        await this.auth.waitFor('auth:initialized', 3000);

        // Initialize chat service
        this.chat = new ChatService(this.apiClient, this.storage);
        await this.chat.waitFor('chat:initialized', 3000);

        // Initialize other services
        this.personality = new PersonalityService(this.apiClient, this.storage);
        this.knowledge = new KnowledgeService(this.apiClient, this.storage);
        this.files = new FileService(this.apiClient, this.storage);

        // Initialize real-time service if authenticated
        if (this.auth.isAuthenticated()) {
            this.realtime = new RealtimeService(this.auth.getToken());
        }
    }

    /**
     * Initialize UI components
     */
    async initializeUIComponents() {
        // Initialize toast manager
        this.toastManager = new ToastManager();

        // Initialize message renderer
        this.messageRenderer = new MessageRenderer(this.i18n);

        // Initialize conversation list
        this.conversationList = new ConversationList(this.chat, this.i18n);

        // Initialize chat interface
        this.chatInterface = new ChatInterface(
            this.chat,
            this.files,
            this.messageRenderer,
            this.i18n
        );

        // Connect services to UI components
        this.connectServicesToUI();
    }

    /**
     * Connect services to UI components
     */
    connectServicesToUI() {
        // Chat service events
        this.chat.on('chat:message-added', (message) => {
            this.chatInterface.addMessage(message);
        });

        this.chat.on('chat:conversation-created', (conversation) => {
            this.conversationList.addConversation(conversation);
            this.chatInterface.setConversation(conversation);
        });

        this.chat.on('chat:conversations-updated', (conversations) => {
            this.conversationList.updateConversations(conversations);
        });

        // Authentication events
        this.auth.on('auth:login-success', (user) => {
            this.handleLoginSuccess(user);
        });

        this.auth.on('auth:logout-success', () => {
            this.handleLogoutSuccess();
        });

        this.auth.on('auth:session-expired', () => {
            this.handleSessionExpired();
        });

        // Error handling
        this.apiClient.on('request:error', (error) => {
            this.handleApiError(error);
        });

        // Network status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.toastManager.showSuccess(this.i18n.t('status.online'));
            this.emit('app:online');
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.toastManager.showWarning(this.i18n.t('status.offline'));
            this.emit('app:offline');
        });
    }

    /**
     * Set up global event listeners
     */
    setupEventListeners() {
        // Theme toggle
        const themeToggle = document.getElementById('theme-toggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.themeManager.toggleTheme();
            });
        }

        // Language selector
        const languageItems = document.querySelectorAll('[data-lang]');
        languageItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = item.getAttribute('data-lang');
                this.i18n.setLanguage(lang);
            });
        });

        // User menu actions
        const userMenuItems = document.querySelectorAll('[data-action]');
        userMenuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const action = item.getAttribute('data-action');
                this.handleUserMenuAction(action);
            });
        });

        // New conversation button
        const newConversationBtn = document.getElementById('new-conversation');
        if (newConversationBtn) {
            newConversationBtn.addEventListener('click', () => {
                this.createNewConversation();
            });
        }

        // Global keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Prevent form submission on Enter in message input
        const messageForm = document.getElementById('message-form');
        if (messageForm) {
            messageForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.chatInterface.sendMessage();
            });
        }
    }

    /**
     * Initialize client-side routing
     */
    initializeRouting() {
        // Simple hash-based routing
        const handleRoute = () => {
            const hash = window.location.hash.slice(1) || 'chat';
            this.navigateToView(hash);
        };

        window.addEventListener('hashchange', handleRoute);
        handleRoute(); // Handle initial route
    }

    /**
     * Navigate to a specific view
     * @param {string} view - View name
     */
    navigateToView(view) {
        const validViews = ['chat', 'knowledge', 'personality', 'settings', 'admin'];

        if (!validViews.includes(view)) {
            view = 'chat';
        }

        this.currentView = view;
        this.updateViewVisibility();
        this.emit('app:view-changed', view);
    }

    /**
     * Update view visibility based on current view
     */
    updateViewVisibility() {
        // Hide all views
        const views = document.querySelectorAll('[data-view]');
        views.forEach(view => {
            view.style.display = 'none';
        });

        // Show current view
        const currentViewElement = document.querySelector(`[data-view="${this.currentView}"]`);
        if (currentViewElement) {
            currentViewElement.style.display = 'block';
        }

        // Update navigation active state
        const navItems = document.querySelectorAll('[data-nav]');
        navItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('data-nav') === this.currentView) {
                item.classList.add('active');
            }
        });
    }

    /**
     * Check authentication status and redirect if needed
     */
    async checkAuthenticationStatus() {
        if (!this.auth.isAuthenticated()) {
            // Show login modal or redirect to login page
            this.showLoginModal();
        } else {
            // Load user data and initialize authenticated features
            await this.initializeAuthenticatedFeatures();
        }
    }

    /**
     * Initialize features that require authentication
     */
    async initializeAuthenticatedFeatures() {
        try {
            // Initialize real-time service
            if (!this.realtime) {
                this.realtime = new RealtimeService(this.auth.getToken());
            }

            // Load user conversations
            await this.chat.loadRecentConversations();

            // Load user personality
            await this.personality.loadUserPersonality();

            // Update UI with user info
            this.updateUserInterface();

        } catch (error) {
            console.error('Failed to initialize authenticated features:', error);
            this.toastManager.showError(this.i18n.t('error.unknown'));
        }
    }

    /**
     * Handle successful login
     * @param {Object} user - User data
     */
    async handleLoginSuccess(user) {
        this.toastManager.showSuccess(this.i18n.t('auth.loginSuccess', { name: user.name }));

        // Initialize authenticated features
        await this.initializeAuthenticatedFeatures();

        // Hide login modal
        this.hideLoginModal();

        // Navigate to chat view
        this.navigateToView('chat');
    }

    /**
     * Handle successful logout
     */
    handleLogoutSuccess() {
        this.toastManager.showInfo(this.i18n.t('auth.logoutSuccess'));

        // Clear application state
        this.clearApplicationState();

        // Show login modal
        this.showLoginModal();
    }

    /**
     * Handle session expiration
     */
    handleSessionExpired() {
        this.toastManager.showWarning(this.i18n.t('auth.sessionExpired'));

        // Clear application state
        this.clearApplicationState();

        // Show login modal
        this.showLoginModal();
    }

    /**
     * Clear application state on logout
     */
    clearApplicationState() {
        // Clear chat data
        this.chat.conversations.clear();
        this.chat.messageCache.clear();
        this.chat.currentConversation = null;

        // Clear UI
        this.conversationList.clear();
        this.chatInterface.clear();

        // Disconnect real-time service
        if (this.realtime) {
            this.realtime.disconnect();
            this.realtime = null;
        }
    }

    /**
     * Handle user menu actions
     * @param {string} action - Action name
     */
    handleUserMenuAction(action) {
        switch (action) {
            case 'personality':
                this.navigateToView('personality');
                break;
            case 'settings':
                this.navigateToView('settings');
                break;
            case 'knowledge':
                this.navigateToView('knowledge');
                break;
            case 'admin':
                if (this.auth.hasRole('admin')) {
                    this.navigateToView('admin');
                } else {
                    this.toastManager.showError(this.i18n.t('error.permission'));
                }
                break;
            case 'logout':
                this.auth.logout();
                break;
        }
    }

    /**
     * Handle keyboard shortcuts
     * @param {KeyboardEvent} e - Keyboard event
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + N: New conversation
        if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
            e.preventDefault();
            this.createNewConversation();
        }

        // Ctrl/Cmd + /: Focus message input
        if ((e.ctrlKey || e.metaKey) && e.key === '/') {
            e.preventDefault();
            const messageInput = document.getElementById('message-input');
            if (messageInput) {
                messageInput.focus();
            }
        }

        // Escape: Close modals
        if (e.key === 'Escape') {
            this.closeModals();
        }
    }

    /**
     * Create new conversation
     */
    async createNewConversation() {
        try {
            const conversation = await this.chat.createConversation();
            this.toastManager.showSuccess(this.i18n.t('chat.conversationCreated'));
        } catch (error) {
            this.toastManager.showError(this.i18n.t('chat.conversationCreateError'));
        }
    }

    /**
     * Handle API errors
     * @param {Error} error - API error
     */
    handleApiError(error) {
        if (error.status === 401) {
            // Authentication error - handled by auth service
            return;
        }

        if (error.status === 403) {
            this.toastManager.showError(this.i18n.t('error.permission'));
            return;
        }

        if (error.status >= 500) {
            this.toastManager.showError(this.i18n.t('error.server'));
            return;
        }

        if (error.isNetworkError) {
            this.toastManager.showError(this.i18n.t('error.network'));
            return;
        }

        // Generic error
        this.toastManager.showError(error.message || this.i18n.t('error.unknown'));
    }

    /**
     * Handle initialization errors
     * @param {Error} error - Initialization error
     */
    handleInitializationError(error) {
        this.errorState = error;

        // Show error screen
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            loadingScreen.innerHTML = `
                <div class="text-center">
                    <i class="bi bi-exclamation-triangle text-danger mb-3" style="font-size: 3rem;"></i>
                    <h4 class="text-danger">Initialization Failed</h4>
                    <p class="text-muted mb-3">${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">
                        <i class="bi bi-arrow-clockwise me-2"></i>Retry
                    </button>
                </div>
            `;
        }

        this.emit('app:error', error);
    }

    /**
     * Show loading screen
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');

        if (loadingScreen) loadingScreen.classList.remove('d-none');
        if (app) app.classList.add('d-none');
    }

    /**
     * Hide loading screen
     */
    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const app = document.getElementById('app');

        if (loadingScreen) loadingScreen.classList.add('d-none');
        if (app) app.classList.remove('d-none');
    }

    /**
     * Show login modal
     */
    showLoginModal() {
        // Implementation would show login modal
        console.log('Show login modal');
    }

    /**
     * Hide login modal
     */
    hideLoginModal() {
        // Implementation would hide login modal
        console.log('Hide login modal');
    }

    /**
     * Close all modals
     */
    closeModals() {
        const modals = document.querySelectorAll('.modal.show');
        modals.forEach(modal => {
            const bsModal = bootstrap.Modal.getInstance(modal);
            if (bsModal) {
                bsModal.hide();
            }
        });
    }

    /**
     * Update user interface with user data
     */
    updateUserInterface() {
        const user = this.auth.getUser();
        if (user) {
            const userNameElement = document.getElementById('user-name');
            if (userNameElement) {
                userNameElement.textContent = user.name;
            }
        }
    }

    // Public API methods
    getVersion() {
        return this.version;
    }

    isReady() {
        return this.isInitialized;
    }

    getCurrentView() {
        return this.currentView;
    }

    getServices() {
        return {
            api: this.apiClient,
            storage: this.storage,
            i18n: this.i18n,
            auth: this.auth,
            chat: this.chat,
            personality: this.personality,
            knowledge: this.knowledge,
            files: this.files,
            realtime: this.realtime
        };
    }
}

// Export for global use
window.WiddxApp = WiddxApp;
