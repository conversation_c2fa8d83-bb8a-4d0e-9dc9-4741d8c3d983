/**
 * ChatInterface - Main chat interface component for WIDDX AI
 * Handles message display, input, and user interactions
 */
class ChatInterface extends EventEmitter {
    constructor(chatService, fileService, messageRenderer, i18n) {
        super();
        this.chatService = chatService;
        this.fileService = fileService;
        this.messageRenderer = messageRenderer;
        this.i18n = i18n;
        
        // DOM elements
        this.messagesContainer = document.getElementById('messages-container');
        this.messagesList = document.getElementById('messages-list');
        this.messageInput = document.getElementById('message-input');
        this.messageForm = document.getElementById('message-form');
        this.sendButton = document.getElementById('send-button');
        this.fileInput = document.getElementById('file-input');
        this.fileUploadBtn = document.getElementById('file-upload-btn');
        this.attachedFiles = document.getElementById('attached-files');
        this.filesPreview = document.getElementById('files-preview');
        this.typingIndicator = document.getElementById('typing-indicator');
        
        // State
        this.currentConversation = null;
        this.attachedFilesList = [];
        this.isTyping = false;
        this.isSending = false;
        this.autoScrollEnabled = true;
        
        this.initialize();
    }

    /**
     * Initialize chat interface
     */
    initialize() {
        this.setupEventListeners();
        this.setupFileUpload();
        this.setupMessageInput();
        this.setupKeyboardShortcuts();
        
        // Load welcome message if no conversation
        if (!this.currentConversation) {
            this.showWelcomeMessage();
        }
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Message form submission
        if (this.messageForm) {
            this.messageForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.sendMessage();
            });
        }

        // File upload button
        if (this.fileUploadBtn) {
            this.fileUploadBtn.addEventListener('click', () => {
                this.fileInput?.click();
            });
        }

        // File input change
        if (this.fileInput) {
            this.fileInput.addEventListener('change', (e) => {
                this.handleFileSelection(e.target.files);
            });
        }

        // Auto-scroll toggle on manual scroll
        if (this.messagesContainer) {
            this.messagesContainer.addEventListener('scroll', () => {
                const { scrollTop, scrollHeight, clientHeight } = this.messagesContainer;
                const isAtBottom = scrollTop + clientHeight >= scrollHeight - 50;
                this.autoScrollEnabled = isAtBottom;
            });
        }

        // Chat service events
        this.chatService.on('chat:message-added', (message) => {
            this.addMessage(message);
        });

        this.chatService.on('chat:message-sending', () => {
            this.setTypingIndicator(true);
        });

        this.chatService.on('chat:message-sent', () => {
            this.setTypingIndicator(false);
            this.clearInput();
        });

        this.chatService.on('chat:message-failed', (message) => {
            this.setTypingIndicator(false);
            this.showMessageError(message);
        });
    }

    /**
     * Set up file upload functionality
     */
    setupFileUpload() {
        // Drag and drop
        if (this.messagesContainer) {
            this.messagesContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
                this.messagesContainer.classList.add('drag-over');
            });

            this.messagesContainer.addEventListener('dragleave', (e) => {
                e.preventDefault();
                this.messagesContainer.classList.remove('drag-over');
            });

            this.messagesContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                this.messagesContainer.classList.remove('drag-over');
                this.handleFileSelection(e.dataTransfer.files);
            });
        }
    }

    /**
     * Set up message input functionality
     */
    setupMessageInput() {
        if (!this.messageInput) return;

        // Auto-resize textarea
        this.messageInput.addEventListener('input', () => {
            this.autoResizeTextarea();
            this.updateSendButton();
        });

        // Handle Enter key
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // Typing indicator (would be implemented with real-time service)
        let typingTimer;
        this.messageInput.addEventListener('input', () => {
            if (!this.isTyping) {
                this.isTyping = true;
                // Send typing start event
            }
            
            clearTimeout(typingTimer);
            typingTimer = setTimeout(() => {
                this.isTyping = false;
                // Send typing stop event
            }, 1000);
        });
    }

    /**
     * Set up keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Focus message input with Ctrl+/
            if ((e.ctrlKey || e.metaKey) && e.key === '/') {
                e.preventDefault();
                this.focusInput();
            }
        });
    }

    /**
     * Send message
     */
    async sendMessage() {
        if (this.isSending) return;

        const content = this.messageInput?.value.trim();
        if (!content && this.attachedFilesList.length === 0) return;

        if (!this.currentConversation) {
            // Create new conversation
            try {
                this.currentConversation = await this.chatService.createConversation({
                    initialMessage: content
                });
            } catch (error) {
                console.error('Failed to create conversation:', error);
                return;
            }
        }

        this.isSending = true;
        this.updateSendButton();

        try {
            // Upload files if any
            let attachments = [];
            if (this.attachedFilesList.length > 0) {
                attachments = await this.uploadAttachedFiles();
            }

            // Send message
            await this.chatService.sendMessage(
                this.currentConversation.id,
                content,
                {
                    attachments,
                    useReasoning: true
                }
            );

        } catch (error) {
            console.error('Failed to send message:', error);
            this.showError('Failed to send message. Please try again.');
        } finally {
            this.isSending = false;
            this.updateSendButton();
        }
    }

    /**
     * Add message to chat interface
     * @param {Object} message - Message object
     */
    addMessage(message) {
        if (!this.messagesList) return;

        const messageElement = this.messageRenderer.renderMessage(message);
        this.messagesList.appendChild(messageElement);

        // Auto-scroll if enabled
        if (this.autoScrollEnabled) {
            this.scrollToBottom();
        }

        // Highlight new messages
        if (message.role === 'assistant') {
            setTimeout(() => {
                messageElement.classList.add('highlight');
                setTimeout(() => {
                    messageElement.classList.remove('highlight');
                }, 2000);
            }, 100);
        }
    }

    /**
     * Set conversation
     * @param {Object} conversation - Conversation object
     */
    async setConversation(conversation) {
        this.currentConversation = conversation;
        
        // Clear current messages
        if (this.messagesList) {
            this.messagesList.innerHTML = '';
        }

        // Load conversation messages
        try {
            const messages = await this.chatService.loadMessages(conversation.id);
            messages.forEach(message => {
                this.addMessage(message);
            });
        } catch (error) {
            console.error('Failed to load messages:', error);
            this.showError('Failed to load conversation messages.');
        }

        // Update conversation title
        this.updateConversationTitle(conversation.title);
    }

    /**
     * Handle file selection
     * @param {FileList} files - Selected files
     */
    handleFileSelection(files) {
        const maxFiles = 5;
        const maxSize = 10 * 1024 * 1024; // 10MB

        for (const file of files) {
            if (this.attachedFilesList.length >= maxFiles) {
                this.showError(`Maximum ${maxFiles} files allowed`);
                break;
            }

            if (file.size > maxSize) {
                this.showError(`File "${file.name}" is too large. Maximum size is 10MB.`);
                continue;
            }

            // Check if file already attached
            if (this.attachedFilesList.some(f => f.name === file.name && f.size === file.size)) {
                continue;
            }

            this.attachedFilesList.push(file);
        }

        this.updateFilesPreview();
    }

    /**
     * Update files preview
     */
    updateFilesPreview() {
        if (!this.filesPreview) return;

        if (this.attachedFilesList.length === 0) {
            this.attachedFiles?.classList.add('d-none');
            return;
        }

        this.attachedFiles?.classList.remove('d-none');
        this.filesPreview.innerHTML = '';

        this.attachedFilesList.forEach((file, index) => {
            const filePreview = document.createElement('div');
            filePreview.className = 'file-preview d-flex align-items-center';
            
            const icon = this.getFileIcon(file.type);
            const size = this.formatFileSize(file.size);

            filePreview.innerHTML = `
                <i class="bi ${icon} file-icon me-2"></i>
                <div class="flex-grow-1">
                    <div class="fw-medium">${file.name}</div>
                    <small class="text-muted">${size}</small>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger remove-file" data-index="${index}">
                    <i class="bi bi-x"></i>
                </button>
            `;

            // Remove file button
            const removeBtn = filePreview.querySelector('.remove-file');
            removeBtn.addEventListener('click', () => {
                this.removeAttachedFile(index);
            });

            this.filesPreview.appendChild(filePreview);
        });
    }

    /**
     * Remove attached file
     * @param {number} index - File index
     */
    removeAttachedFile(index) {
        this.attachedFilesList.splice(index, 1);
        this.updateFilesPreview();
    }

    /**
     * Upload attached files
     * @returns {Promise<Array>} - Array of uploaded file data
     */
    async uploadAttachedFiles() {
        const uploadPromises = this.attachedFilesList.map(async (file) => {
            try {
                const formData = new FormData();
                formData.append('file', file);
                
                const result = await this.fileService.uploadFile(formData);
                return result;
            } catch (error) {
                console.error(`Failed to upload file ${file.name}:`, error);
                throw error;
            }
        });

        const results = await Promise.all(uploadPromises);
        this.attachedFilesList = []; // Clear after upload
        this.updateFilesPreview();
        
        return results;
    }

    /**
     * Auto-resize textarea
     */
    autoResizeTextarea() {
        if (!this.messageInput) return;

        this.messageInput.style.height = 'auto';
        const newHeight = Math.min(this.messageInput.scrollHeight, 120);
        this.messageInput.style.height = newHeight + 'px';
    }

    /**
     * Update send button state
     */
    updateSendButton() {
        if (!this.sendButton) return;

        const hasContent = this.messageInput?.value.trim() || this.attachedFilesList.length > 0;
        const isDisabled = this.isSending || !hasContent;

        this.sendButton.disabled = isDisabled;
        
        if (this.isSending) {
            this.sendButton.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        } else {
            this.sendButton.innerHTML = '<i class="bi bi-send"></i>';
        }
    }

    /**
     * Set typing indicator
     * @param {boolean} show - Whether to show typing indicator
     */
    setTypingIndicator(show) {
        if (!this.typingIndicator) return;

        if (show) {
            this.typingIndicator.classList.remove('d-none');
        } else {
            this.typingIndicator.classList.add('d-none');
        }
    }

    /**
     * Clear input and reset state
     */
    clearInput() {
        if (this.messageInput) {
            this.messageInput.value = '';
            this.autoResizeTextarea();
        }
        
        this.attachedFilesList = [];
        this.updateFilesPreview();
        this.updateSendButton();
    }

    /**
     * Focus message input
     */
    focusInput() {
        this.messageInput?.focus();
    }

    /**
     * Scroll to bottom of messages
     */
    scrollToBottom() {
        if (this.messagesContainer) {
            this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
        }
    }

    /**
     * Update conversation title
     * @param {string} title - Conversation title
     */
    updateConversationTitle(title) {
        const titleElement = document.getElementById('conversation-title');
        if (titleElement) {
            titleElement.textContent = title;
        }
    }

    /**
     * Show welcome message
     */
    showWelcomeMessage() {
        if (!this.messagesList) return;

        const welcomeMessage = {
            id: 'welcome',
            role: 'assistant',
            content: this.i18n.t('chat.welcomeMessage'),
            created_at: new Date().toISOString()
        };

        this.addMessage(welcomeMessage);
    }

    /**
     * Show error message
     * @param {string} message - Error message
     */
    showError(message) {
        // This would typically use the toast manager
        console.error(message);
    }

    /**
     * Show message error
     * @param {Object} message - Failed message
     */
    showMessageError(message) {
        // Update message element to show error state
        const messageElement = document.querySelector(`[data-message-id="${message.id}"]`);
        if (messageElement) {
            messageElement.classList.add('message-error');
            
            // Add retry button
            const retryBtn = document.createElement('button');
            retryBtn.className = 'btn btn-sm btn-outline-primary mt-2';
            retryBtn.innerHTML = '<i class="bi bi-arrow-clockwise me-1"></i>Retry';
            retryBtn.addEventListener('click', () => {
                this.retryMessage(message);
            });
            
            messageElement.appendChild(retryBtn);
        }
    }

    /**
     * Retry failed message
     * @param {Object} message - Failed message
     */
    async retryMessage(message) {
        try {
            await this.chatService.sendMessage(
                message.conversation_id,
                message.content,
                { attachments: message.attachments }
            );
        } catch (error) {
            console.error('Retry failed:', error);
        }
    }

    /**
     * Get file icon based on file type
     * @param {string} fileType - File MIME type
     * @returns {string} - Bootstrap icon class
     */
    getFileIcon(fileType) {
        if (fileType.startsWith('image/')) return 'bi-image';
        if (fileType.startsWith('video/')) return 'bi-camera-video';
        if (fileType.startsWith('audio/')) return 'bi-music-note';
        if (fileType.includes('pdf')) return 'bi-file-pdf';
        if (fileType.includes('word')) return 'bi-file-word';
        if (fileType.includes('excel')) return 'bi-file-excel';
        if (fileType.includes('powerpoint')) return 'bi-file-ppt';
        if (fileType.includes('text')) return 'bi-file-text';
        if (fileType.includes('json')) return 'bi-file-code';
        return 'bi-file';
    }

    /**
     * Format file size for display
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Clear chat interface
     */
    clear() {
        if (this.messagesList) {
            this.messagesList.innerHTML = '';
        }
        this.clearInput();
        this.currentConversation = null;
        this.updateConversationTitle('WIDDX AI Assistant');
    }
}

// Export for use in other modules
window.ChatInterface = ChatInterface;
