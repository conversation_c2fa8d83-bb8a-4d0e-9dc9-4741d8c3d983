/**
 * ConversationList - Conversation list component for WIDDX AI
 * Handles displaying and managing conversation list in sidebar
 */
class ConversationList extends EventEmitter {
    constructor(chatService, i18n) {
        super();
        this.chatService = chatService;
        this.i18n = i18n;
        this.conversations = [];
        this.currentConversationId = null;
        this.searchQuery = '';
        this.isLoading = false;
        
        // DOM elements
        this.container = document.getElementById('conversations-list');
        this.searchInput = null;
        
        this.initialize();
    }

    /**
     * Initialize conversation list
     */
    initialize() {
        this.setupEventListeners();
        this.createSearchInput();
        this.loadConversations();
    }

    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Chat service events
        this.chatService.on('chat:conversations-updated', (conversations) => {
            this.updateConversations(conversations);
        });

        this.chatService.on('chat:conversation-created', (conversation) => {
            this.addConversation(conversation);
        });

        this.chatService.on('chat:conversation-deleted', (conversationId) => {
            this.removeConversation(conversationId);
        });

        this.chatService.on('chat:conversation-changed', (conversation) => {
            this.setActiveConversation(conversation.id);
        });
    }

    /**
     * Create search input
     */
    createSearchInput() {
        if (!this.container) return;

        const searchContainer = document.createElement('div');
        searchContainer.className = 'mb-3';
        searchContainer.innerHTML = `
            <div class="input-group input-group-sm">
                <span class="input-group-text bg-dark border-secondary">
                    <i class="bi bi-search text-muted"></i>
                </span>
                <input type="text" class="form-control bg-dark border-secondary text-light" 
                       placeholder="Search conversations..." id="conversation-search">
            </div>
        `;

        this.container.parentNode.insertBefore(searchContainer, this.container);
        this.searchInput = searchContainer.querySelector('#conversation-search');

        // Search functionality
        let searchTimeout;
        this.searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.searchQuery = e.target.value.toLowerCase();
                this.filterConversations();
            }, 300);
        });
    }

    /**
     * Load conversations
     */
    async loadConversations() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoadingState();

        try {
            const conversations = await this.chatService.loadRecentConversations();
            this.conversations = conversations;
            this.renderConversations();
        } catch (error) {
            console.error('Failed to load conversations:', error);
            this.showErrorState();
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Update conversations list
     * @param {Array} conversations - Array of conversations
     */
    updateConversations(conversations) {
        this.conversations = conversations;
        this.renderConversations();
    }

    /**
     * Add new conversation
     * @param {Object} conversation - Conversation object
     */
    addConversation(conversation) {
        // Add to beginning of list
        this.conversations.unshift(conversation);
        this.renderConversations();
        this.setActiveConversation(conversation.id);
    }

    /**
     * Remove conversation
     * @param {number} conversationId - Conversation ID
     */
    removeConversation(conversationId) {
        this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
        this.renderConversations();
        
        // If removed conversation was active, clear selection
        if (this.currentConversationId === conversationId) {
            this.currentConversationId = null;
        }
    }

    /**
     * Set active conversation
     * @param {number} conversationId - Conversation ID
     */
    setActiveConversation(conversationId) {
        this.currentConversationId = conversationId;
        this.updateActiveState();
    }

    /**
     * Filter conversations based on search query
     */
    filterConversations() {
        this.renderConversations();
    }

    /**
     * Render conversations list
     */
    renderConversations() {
        if (!this.container) return;

        const filteredConversations = this.getFilteredConversations();

        if (filteredConversations.length === 0) {
            this.showEmptyState();
            return;
        }

        const conversationsHtml = filteredConversations.map(conversation => 
            this.renderConversationItem(conversation)
        ).join('');

        this.container.innerHTML = conversationsHtml;
        this.updateActiveState();
        this.attachEventListeners();
    }

    /**
     * Render individual conversation item
     * @param {Object} conversation - Conversation object
     * @returns {string} - HTML string
     */
    renderConversationItem(conversation) {
        const title = this.escapeHtml(conversation.title || 'New Conversation');
        const preview = this.getConversationPreview(conversation);
        const timestamp = this.formatTimestamp(conversation.last_activity_at || conversation.created_at);
        const messageCount = conversation.messages_count || 0;
        const isActive = conversation.id === this.currentConversationId;

        return `
            <div class="conversation-item ${isActive ? 'active' : ''}" 
                 data-conversation-id="${conversation.id}">
                <div class="d-flex align-items-start">
                    <div class="flex-grow-1">
                        <div class="conversation-title">${title}</div>
                        <div class="conversation-preview">${preview}</div>
                        <div class="conversation-meta mt-1">
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i>
                                ${timestamp}
                                <i class="bi bi-chat-dots ms-2 me-1"></i>
                                ${messageCount}
                            </small>
                        </div>
                    </div>
                    <div class="conversation-actions">
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" 
                                    type="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#" data-action="rename">
                                    <i class="bi bi-pencil me-2"></i>Rename
                                </a></li>
                                <li><a class="dropdown-item" href="#" data-action="export">
                                    <i class="bi bi-download me-2"></i>Export
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" data-action="delete">
                                    <i class="bi bi-trash me-2"></i>Delete
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get conversation preview text
     * @param {Object} conversation - Conversation object
     * @returns {string} - Preview text
     */
    getConversationPreview(conversation) {
        if (conversation.last_message) {
            const content = conversation.last_message.content || '';
            return this.escapeHtml(content.substring(0, 60) + (content.length > 60 ? '...' : ''));
        }
        return 'No messages yet';
    }

    /**
     * Get filtered conversations based on search query
     * @returns {Array} - Filtered conversations
     */
    getFilteredConversations() {
        if (!this.searchQuery) {
            return this.conversations;
        }

        return this.conversations.filter(conversation => {
            const title = (conversation.title || '').toLowerCase();
            const preview = this.getConversationPreview(conversation).toLowerCase();
            return title.includes(this.searchQuery) || preview.includes(this.searchQuery);
        });
    }

    /**
     * Attach event listeners to conversation items
     */
    attachEventListeners() {
        if (!this.container) return;

        // Conversation click events
        const conversationItems = this.container.querySelectorAll('.conversation-item');
        conversationItems.forEach(item => {
            item.addEventListener('click', (e) => {
                // Don't trigger if clicking on dropdown
                if (e.target.closest('.conversation-actions')) return;
                
                const conversationId = parseInt(item.getAttribute('data-conversation-id'));
                this.selectConversation(conversationId);
            });
        });

        // Action menu events
        const actionItems = this.container.querySelectorAll('[data-action]');
        actionItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                const action = item.getAttribute('data-action');
                const conversationItem = item.closest('.conversation-item');
                const conversationId = parseInt(conversationItem.getAttribute('data-conversation-id'));
                
                this.handleConversationAction(action, conversationId);
            });
        });
    }

    /**
     * Select conversation
     * @param {number} conversationId - Conversation ID
     */
    async selectConversation(conversationId) {
        try {
            await this.chatService.setCurrentConversation(conversationId);
            this.emit('conversation:selected', conversationId);
        } catch (error) {
            console.error('Failed to select conversation:', error);
        }
    }

    /**
     * Handle conversation actions
     * @param {string} action - Action type
     * @param {number} conversationId - Conversation ID
     */
    async handleConversationAction(action, conversationId) {
        const conversation = this.conversations.find(conv => conv.id === conversationId);
        if (!conversation) return;

        switch (action) {
            case 'rename':
                this.renameConversation(conversation);
                break;
            case 'export':
                this.exportConversation(conversation);
                break;
            case 'delete':
                this.deleteConversation(conversation);
                break;
        }
    }

    /**
     * Rename conversation
     * @param {Object} conversation - Conversation object
     */
    async renameConversation(conversation) {
        const newTitle = prompt('Enter new conversation title:', conversation.title);
        if (!newTitle || newTitle === conversation.title) return;

        try {
            await this.chatService.updateConversationTitle(conversation.id, newTitle);
            conversation.title = newTitle;
            this.renderConversations();
        } catch (error) {
            console.error('Failed to rename conversation:', error);
            alert('Failed to rename conversation. Please try again.');
        }
    }

    /**
     * Export conversation
     * @param {Object} conversation - Conversation object
     */
    async exportConversation(conversation) {
        try {
            const blob = await this.chatService.exportConversation(conversation.id, 'json');
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `conversation_${conversation.id}.json`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Failed to export conversation:', error);
            alert('Failed to export conversation. Please try again.');
        }
    }

    /**
     * Delete conversation
     * @param {Object} conversation - Conversation object
     */
    async deleteConversation(conversation) {
        if (!confirm(`Are you sure you want to delete "${conversation.title}"?`)) return;

        try {
            await this.chatService.deleteConversation(conversation.id);
        } catch (error) {
            console.error('Failed to delete conversation:', error);
            alert('Failed to delete conversation. Please try again.');
        }
    }

    /**
     * Update active state of conversation items
     */
    updateActiveState() {
        if (!this.container) return;

        const items = this.container.querySelectorAll('.conversation-item');
        items.forEach(item => {
            const conversationId = parseInt(item.getAttribute('data-conversation-id'));
            if (conversationId === this.currentConversationId) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * Show loading state
     */
    showLoadingState() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border spinner-border-sm text-primary mb-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <div class="text-muted">Loading conversations...</div>
            </div>
        `;
    }

    /**
     * Show empty state
     */
    showEmptyState() {
        if (!this.container) return;

        const message = this.searchQuery 
            ? 'No conversations match your search'
            : 'No conversations yet. Start a new conversation to begin!';

        this.container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-chat-dots text-muted mb-2" style="font-size: 2rem;"></i>
                <div class="text-muted">${message}</div>
            </div>
        `;
    }

    /**
     * Show error state
     */
    showErrorState() {
        if (!this.container) return;

        this.container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-exclamation-triangle text-danger mb-2" style="font-size: 2rem;"></i>
                <div class="text-danger mb-2">Failed to load conversations</div>
                <button class="btn btn-sm btn-outline-primary" onclick="this.loadConversations()">
                    <i class="bi bi-arrow-clockwise me-1"></i>Retry
                </button>
            </div>
        `;
    }

    /**
     * Format timestamp
     * @param {string} timestamp - ISO timestamp
     * @returns {string} - Formatted timestamp
     */
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        
        return date.toLocaleDateString();
    }

    /**
     * Escape HTML characters
     * @param {string} text - Text to escape
     * @returns {string} - Escaped text
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Clear conversation list
     */
    clear() {
        this.conversations = [];
        this.currentConversationId = null;
        if (this.container) {
            this.container.innerHTML = '';
        }
        if (this.searchInput) {
            this.searchInput.value = '';
        }
        this.searchQuery = '';
    }

    /**
     * Refresh conversations
     */
    async refresh() {
        await this.loadConversations();
    }
}

// Export for use in other modules
window.ConversationList = ConversationList;
