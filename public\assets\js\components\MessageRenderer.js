/**
 * MessageRenderer - Message rendering component for WIDDX AI
 * Handles rendering of chat messages with markdown, code highlighting, and reasoning
 */
class MessageRenderer {
    constructor(i18n) {
        this.i18n = i18n;
        this.markedOptions = {
            highlight: (code, lang) => {
                if (lang && hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(code, { language: lang }).value;
                    } catch (err) {
                        console.warn('Syntax highlighting failed:', err);
                    }
                }
                return hljs.highlightAuto(code).value;
            },
            breaks: true,
            gfm: true
        };
        
        // Configure marked
        if (typeof marked !== 'undefined') {
            marked.setOptions(this.markedOptions);
        }
    }

    /**
     * Render a message element
     * @param {Object} message - Message object
     * @returns {HTMLElement} - Rendered message element
     */
    renderMessage(message) {
        const messageElement = document.createElement('div');
        messageElement.className = `message-bubble ${message.role}-message mb-3`;
        messageElement.setAttribute('data-message-id', message.id);

        if (message.role === 'user') {
            messageElement.innerHTML = this.renderUserMessage(message);
        } else {
            messageElement.innerHTML = this.renderAssistantMessage(message);
        }

        // Add message actions
        this.addMessageActions(messageElement, message);

        return messageElement;
    }

    /**
     * Render user message
     * @param {Object} message - User message object
     * @returns {string} - HTML string
     */
    renderUserMessage(message) {
        const timestamp = this.formatTimestamp(message.created_at);
        const content = this.escapeHtml(message.content);
        const attachments = this.renderAttachments(message.attachments);

        return `
            <div class="d-flex align-items-start justify-content-end">
                <div class="flex-grow-1 me-3">
                    <div class="message-content user-message">
                        ${content}
                        ${attachments}
                    </div>
                    <div class="message-meta text-end mt-1">
                        <small class="text-muted">
                            <i class="bi bi-clock me-1"></i>
                            <span class="timestamp">${timestamp}</span>
                            ${this.renderMessageStatus(message)}
                        </small>
                    </div>
                    <div class="message-actions"></div>
                </div>
                <div class="avatar user-avatar">
                    <i class="bi bi-person-fill text-white"></i>
                </div>
            </div>
        `;
    }

    /**
     * Render assistant message
     * @param {Object} message - Assistant message object
     * @returns {string} - HTML string
     */
    renderAssistantMessage(message) {
        const timestamp = this.formatTimestamp(message.created_at);
        const content = this.renderMarkdown(message.content);
        const reasoning = this.renderReasoning(message.reasoning_steps);
        const toolCalls = this.renderToolCalls(message.tool_calls);
        const model = message.metadata?.model || 'AI';

        return `
            <div class="d-flex align-items-start">
                <div class="avatar ai-avatar me-3">
                    <i class="bi bi-robot text-white"></i>
                </div>
                <div class="flex-grow-1">
                    <div class="message-content ai-message">
                        ${content}
                        ${toolCalls}
                    </div>
                    ${reasoning}
                    <div class="message-meta mt-1">
                        <small class="text-muted">
                            <i class="bi bi-cpu me-1"></i>
                            <span class="model-name">${model}</span>
                            <i class="bi bi-clock ms-2 me-1"></i>
                            <span class="timestamp">${timestamp}</span>
                            ${this.renderConfidenceScore(message.confidence_score)}
                        </small>
                    </div>
                    <div class="message-actions"></div>
                </div>
            </div>
        `;
    }

    /**
     * Render markdown content
     * @param {string} content - Markdown content
     * @returns {string} - HTML string
     */
    renderMarkdown(content) {
        if (!content) return '';

        try {
            // Use marked if available, otherwise fallback to simple formatting
            if (typeof marked !== 'undefined') {
                return marked.parse(content);
            } else {
                return this.simpleMarkdownParse(content);
            }
        } catch (error) {
            console.warn('Markdown parsing failed:', error);
            return this.escapeHtml(content);
        }
    }

    /**
     * Simple markdown parser fallback
     * @param {string} content - Markdown content
     * @returns {string} - HTML string
     */
    simpleMarkdownParse(content) {
        let html = this.escapeHtml(content);

        // Code blocks
        html = html.replace(/```(\w+)?\n([\s\S]*?)```/g, (match, lang, code) => {
            const highlightedCode = this.highlightCode(code, lang);
            return `<pre><code class="language-${lang || 'text'}">${highlightedCode}</code></pre>`;
        });

        // Inline code
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

        // Bold
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Italic
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Links
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener">$1</a>');

        // Line breaks
        html = html.replace(/\n/g, '<br>');

        return html;
    }

    /**
     * Highlight code
     * @param {string} code - Code content
     * @param {string} language - Programming language
     * @returns {string} - Highlighted code
     */
    highlightCode(code, language) {
        if (typeof hljs !== 'undefined') {
            try {
                if (language && hljs.getLanguage(language)) {
                    return hljs.highlight(code, { language }).value;
                } else {
                    return hljs.highlightAuto(code).value;
                }
            } catch (error) {
                console.warn('Code highlighting failed:', error);
            }
        }
        return this.escapeHtml(code);
    }

    /**
     * Render reasoning steps
     * @param {Array} reasoningSteps - Array of reasoning steps
     * @returns {string} - HTML string
     */
    renderReasoning(reasoningSteps) {
        if (!reasoningSteps || reasoningSteps.length === 0) {
            return '';
        }

        const stepsHtml = reasoningSteps.map((step, index) => {
            const confidence = step.confidence ? `(${Math.round(step.confidence * 100)}%)` : '';
            return `
                <div class="reasoning-step">
                    <div class="reasoning-step-title">
                        <i class="bi bi-arrow-right me-1"></i>
                        Step ${index + 1}: ${this.escapeHtml(step.title)} ${confidence}
                    </div>
                    <div class="reasoning-step-content">
                        ${this.escapeHtml(step.content)}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="reasoning-steps mt-3" style="display: none;">
                <div class="d-flex align-items-center mb-2">
                    <i class="bi bi-lightbulb text-primary me-2"></i>
                    <strong>Chain of Thought</strong>
                </div>
                ${stepsHtml}
            </div>
        `;
    }

    /**
     * Render tool calls
     * @param {Array} toolCalls - Array of tool calls
     * @returns {string} - HTML string
     */
    renderToolCalls(toolCalls) {
        if (!toolCalls || toolCalls.length === 0) {
            return '';
        }

        const toolsHtml = toolCalls.map(tool => {
            const icon = this.getToolIcon(tool.tool_name);
            const result = tool.result ? this.escapeHtml(JSON.stringify(tool.result, null, 2)) : '';
            
            return `
                <div class="tool-call mt-2 p-2 bg-dark rounded">
                    <div class="d-flex align-items-center mb-1">
                        <i class="bi ${icon} text-info me-2"></i>
                        <strong class="text-info">${this.escapeHtml(tool.tool_name)}</strong>
                    </div>
                    ${tool.parameters ? `
                        <div class="tool-parameters mb-2">
                            <small class="text-muted">Parameters:</small>
                            <pre class="mt-1 mb-0"><code>${this.escapeHtml(JSON.stringify(tool.parameters, null, 2))}</code></pre>
                        </div>
                    ` : ''}
                    ${result ? `
                        <div class="tool-result">
                            <small class="text-muted">Result:</small>
                            <pre class="mt-1 mb-0"><code>${result}</code></pre>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');

        return `
            <div class="tool-calls mt-2">
                ${toolsHtml}
            </div>
        `;
    }

    /**
     * Render message attachments
     * @param {Array} attachments - Array of attachments
     * @returns {string} - HTML string
     */
    renderAttachments(attachments) {
        if (!attachments || attachments.length === 0) {
            return '';
        }

        const attachmentsHtml = attachments.map(attachment => {
            const icon = this.getFileIcon(attachment.mime_type);
            const size = this.formatFileSize(attachment.size);
            
            return `
                <div class="attachment d-flex align-items-center mt-2 p-2 bg-dark rounded">
                    <i class="bi ${icon} text-primary me-2"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium">${this.escapeHtml(attachment.name)}</div>
                        <small class="text-muted">${size}</small>
                    </div>
                    <button class="btn btn-sm btn-outline-primary" onclick="downloadAttachment(${attachment.id})">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
            `;
        }).join('');

        return `
            <div class="attachments mt-2">
                ${attachmentsHtml}
            </div>
        `;
    }

    /**
     * Render message status
     * @param {Object} message - Message object
     * @returns {string} - HTML string
     */
    renderMessageStatus(message) {
        if (message.status === 'sending') {
            return '<i class="bi bi-clock text-warning ms-2" title="Sending..."></i>';
        } else if (message.status === 'failed') {
            return '<i class="bi bi-exclamation-triangle text-danger ms-2" title="Failed to send"></i>';
        } else if (message.status === 'sent') {
            return '<i class="bi bi-check text-success ms-2" title="Sent"></i>';
        }
        return '';
    }

    /**
     * Render confidence score
     * @param {number} confidence - Confidence score (0-1)
     * @returns {string} - HTML string
     */
    renderConfidenceScore(confidence) {
        if (!confidence) return '';

        const percentage = Math.round(confidence * 100);
        const color = confidence >= 0.8 ? 'success' : confidence >= 0.6 ? 'warning' : 'danger';

        return `
            <i class="bi bi-speedometer2 ms-2 me-1"></i>
            <span class="text-${color}">${percentage}%</span>
        `;
    }

    /**
     * Add message actions
     * @param {HTMLElement} messageElement - Message element
     * @param {Object} message - Message object
     */
    addMessageActions(messageElement, message) {
        const actionsContainer = messageElement.querySelector('.message-actions');
        if (!actionsContainer) return;

        const actions = [];

        // Copy action
        actions.push({
            icon: 'bi-clipboard',
            title: this.i18n.t('chat.copy'),
            action: () => this.copyMessage(message)
        });

        // Regenerate action (for assistant messages)
        if (message.role === 'assistant') {
            actions.push({
                icon: 'bi-arrow-clockwise',
                title: this.i18n.t('chat.regenerate'),
                action: () => this.regenerateMessage(message)
            });

            // Toggle reasoning action
            if (message.reasoning_steps && message.reasoning_steps.length > 0) {
                actions.push({
                    icon: 'bi-lightbulb',
                    title: this.i18n.t('chat.reasoning'),
                    action: () => this.toggleReasoning(messageElement)
                });
            }
        }

        // Delete action
        actions.push({
            icon: 'bi-trash',
            title: this.i18n.t('common.delete'),
            action: () => this.deleteMessage(message),
            className: 'text-danger'
        });

        // Render actions
        const actionsHtml = actions.map(action => `
            <button class="message-action-btn ${action.className || ''}" 
                    title="${action.title}" 
                    data-action="${action.icon}">
                <i class="bi ${action.icon}"></i>
            </button>
        `).join('');

        actionsContainer.innerHTML = actionsHtml;

        // Add event listeners
        actions.forEach((action, index) => {
            const button = actionsContainer.children[index];
            button.addEventListener('click', action.action);
        });
    }

    /**
     * Copy message content
     * @param {Object} message - Message object
     */
    copyMessage(message) {
        navigator.clipboard.writeText(message.content).then(() => {
            // Show success feedback
            console.log('Message copied to clipboard');
        }).catch(err => {
            console.error('Failed to copy message:', err);
        });
    }

    /**
     * Regenerate message
     * @param {Object} message - Message object
     */
    regenerateMessage(message) {
        // Emit event for parent component to handle
        document.dispatchEvent(new CustomEvent('message:regenerate', {
            detail: { message }
        }));
    }

    /**
     * Toggle reasoning display
     * @param {HTMLElement} messageElement - Message element
     */
    toggleReasoning(messageElement) {
        const reasoningElement = messageElement.querySelector('.reasoning-steps');
        if (reasoningElement) {
            const isVisible = reasoningElement.style.display !== 'none';
            reasoningElement.style.display = isVisible ? 'none' : 'block';
        }
    }

    /**
     * Delete message
     * @param {Object} message - Message object
     */
    deleteMessage(message) {
        if (confirm(this.i18n.t('chat.confirmDelete'))) {
            // Emit event for parent component to handle
            document.dispatchEvent(new CustomEvent('message:delete', {
                detail: { message }
            }));
        }
    }

    /**
     * Format timestamp
     * @param {string} timestamp - ISO timestamp
     * @returns {string} - Formatted timestamp
     */
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        if (diffDays < 7) return `${diffDays}d ago`;
        
        return date.toLocaleDateString();
    }

    /**
     * Format file size
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get tool icon
     * @param {string} toolName - Tool name
     * @returns {string} - Bootstrap icon class
     */
    getToolIcon(toolName) {
        const iconMap = {
            'calculator': 'bi-calculator',
            'web_search': 'bi-search',
            'code_analyzer': 'bi-code-slash',
            'data_analyzer': 'bi-graph-up',
            'summarizer': 'bi-file-text'
        };
        return iconMap[toolName] || 'bi-gear';
    }

    /**
     * Get file icon
     * @param {string} mimeType - File MIME type
     * @returns {string} - Bootstrap icon class
     */
    getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return 'bi-image';
        if (mimeType.startsWith('video/')) return 'bi-camera-video';
        if (mimeType.startsWith('audio/')) return 'bi-music-note';
        if (mimeType.includes('pdf')) return 'bi-file-pdf';
        if (mimeType.includes('word')) return 'bi-file-word';
        if (mimeType.includes('excel')) return 'bi-file-excel';
        if (mimeType.includes('text')) return 'bi-file-text';
        return 'bi-file';
    }

    /**
     * Escape HTML characters
     * @param {string} text - Text to escape
     * @returns {string} - Escaped text
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Export for use in other modules
window.MessageRenderer = MessageRenderer;
