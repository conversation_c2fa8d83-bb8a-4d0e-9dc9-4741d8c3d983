/**
 * ToastManager - Toast notification system for WIDDX AI
 * Manages Bootstrap toast notifications with different types and positions
 */
class ToastManager {
    constructor() {
        this.container = document.getElementById('toast-container');
        this.toasts = new Map();
        this.defaultDuration = 5000;
        this.maxToasts = 5;
        
        this.ensureContainer();
    }

    /**
     * Ensure toast container exists
     */
    ensureContainer() {
        if (!this.container) {
            this.container = document.createElement('div');
            this.container.id = 'toast-container';
            this.container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            this.container.style.zIndex = '9999';
            document.body.appendChild(this.container);
        }
    }

    /**
     * Create and show a toast
     * @param {string} message - Toast message
     * @param {string} type - Toast type (success, error, warning, info)
     * @param {Object} options - Toast options
     * @returns {string} - Toast ID
     */
    show(message, type = 'info', options = {}) {
        const toastId = this.generateId();
        const duration = options.duration || this.defaultDuration;
        const title = options.title || this.getDefaultTitle(type);
        const persistent = options.persistent || false;

        // Remove oldest toast if we have too many
        if (this.toasts.size >= this.maxToasts) {
            this.removeOldestToast();
        }

        // Create toast element
        const toastElement = this.createToastElement(toastId, message, type, title, persistent);
        
        // Add to container
        this.container.appendChild(toastElement);
        
        // Initialize Bootstrap toast
        const bsToast = new bootstrap.Toast(toastElement, {
            autohide: !persistent,
            delay: duration
        });

        // Store toast reference
        this.toasts.set(toastId, {
            element: toastElement,
            bootstrap: bsToast,
            type,
            timestamp: Date.now()
        });

        // Show toast
        bsToast.show();

        // Set up auto-removal
        if (!persistent) {
            setTimeout(() => {
                this.remove(toastId);
            }, duration + 500); // Extra time for animation
        }

        // Set up event listeners
        toastElement.addEventListener('hidden.bs.toast', () => {
            this.remove(toastId);
        });

        return toastId;
    }

    /**
     * Show success toast
     * @param {string} message - Success message
     * @param {Object} options - Toast options
     * @returns {string} - Toast ID
     */
    showSuccess(message, options = {}) {
        return this.show(message, 'success', options);
    }

    /**
     * Show error toast
     * @param {string} message - Error message
     * @param {Object} options - Toast options
     * @returns {string} - Toast ID
     */
    showError(message, options = {}) {
        return this.show(message, 'error', { ...options, persistent: true });
    }

    /**
     * Show warning toast
     * @param {string} message - Warning message
     * @param {Object} options - Toast options
     * @returns {string} - Toast ID
     */
    showWarning(message, options = {}) {
        return this.show(message, 'warning', options);
    }

    /**
     * Show info toast
     * @param {string} message - Info message
     * @param {Object} options - Toast options
     * @returns {string} - Toast ID
     */
    showInfo(message, options = {}) {
        return this.show(message, 'info', options);
    }

    /**
     * Create toast HTML element
     * @param {string} id - Toast ID
     * @param {string} message - Toast message
     * @param {string} type - Toast type
     * @param {string} title - Toast title
     * @param {boolean} persistent - Whether toast is persistent
     * @returns {HTMLElement} - Toast element
     */
    createToastElement(id, message, type, title, persistent) {
        const toast = document.createElement('div');
        toast.id = id;
        toast.className = `toast toast-${type}`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        const icon = this.getTypeIcon(type);
        const iconColor = this.getTypeIconColor(type);

        toast.innerHTML = `
            <div class="toast-header">
                <i class="bi ${icon} ${iconColor} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <small class="text-muted">${this.formatTime(new Date())}</small>
                ${persistent ? '' : '<button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>'}
            </div>
            <div class="toast-body">
                ${message}
                ${persistent ? `
                    <div class="mt-2 pt-2 border-top">
                        <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-dismiss="toast">
                            <i class="bi bi-x me-1"></i>Dismiss
                        </button>
                    </div>
                ` : ''}
            </div>
        `;

        return toast;
    }

    /**
     * Remove a toast
     * @param {string} toastId - Toast ID
     */
    remove(toastId) {
        const toast = this.toasts.get(toastId);
        if (toast) {
            // Hide toast first
            toast.bootstrap.hide();
            
            // Remove from DOM after animation
            setTimeout(() => {
                if (toast.element.parentNode) {
                    toast.element.parentNode.removeChild(toast.element);
                }
                this.toasts.delete(toastId);
            }, 300);
        }
    }

    /**
     * Remove oldest toast
     */
    removeOldestToast() {
        let oldestId = null;
        let oldestTime = Date.now();

        for (const [id, toast] of this.toasts) {
            if (toast.timestamp < oldestTime) {
                oldestTime = toast.timestamp;
                oldestId = id;
            }
        }

        if (oldestId) {
            this.remove(oldestId);
        }
    }

    /**
     * Clear all toasts
     */
    clearAll() {
        for (const [id] of this.toasts) {
            this.remove(id);
        }
    }

    /**
     * Get default title for toast type
     * @param {string} type - Toast type
     * @returns {string} - Default title
     */
    getDefaultTitle(type) {
        const titles = {
            success: 'Success',
            error: 'Error',
            warning: 'Warning',
            info: 'Information'
        };
        return titles[type] || 'Notification';
    }

    /**
     * Get icon for toast type
     * @param {string} type - Toast type
     * @returns {string} - Bootstrap icon class
     */
    getTypeIcon(type) {
        const icons = {
            success: 'bi-check-circle-fill',
            error: 'bi-exclamation-triangle-fill',
            warning: 'bi-exclamation-triangle-fill',
            info: 'bi-info-circle-fill'
        };
        return icons[type] || 'bi-info-circle-fill';
    }

    /**
     * Get icon color for toast type
     * @param {string} type - Toast type
     * @returns {string} - CSS color class
     */
    getTypeIconColor(type) {
        const colors = {
            success: 'text-success',
            error: 'text-danger',
            warning: 'text-warning',
            info: 'text-info'
        };
        return colors[type] || 'text-info';
    }

    /**
     * Format time for display
     * @param {Date} date - Date to format
     * @returns {string} - Formatted time
     */
    formatTime(date) {
        return date.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    /**
     * Generate unique toast ID
     * @returns {string} - Unique ID
     */
    generateId() {
        return `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Show loading toast
     * @param {string} message - Loading message
     * @returns {string} - Toast ID
     */
    showLoading(message = 'Loading...') {
        const toastId = this.generateId();
        const toastElement = document.createElement('div');
        toastElement.id = toastId;
        toastElement.className = 'toast';
        toastElement.setAttribute('role', 'alert');
        toastElement.setAttribute('aria-live', 'assertive');
        toastElement.setAttribute('aria-atomic', 'true');

        toastElement.innerHTML = `
            <div class="toast-header">
                <div class="spinner-border spinner-border-sm text-primary me-2" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <strong class="me-auto">Loading</strong>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;

        this.container.appendChild(toastElement);

        const bsToast = new bootstrap.Toast(toastElement, {
            autohide: false
        });

        this.toasts.set(toastId, {
            element: toastElement,
            bootstrap: bsToast,
            type: 'loading',
            timestamp: Date.now()
        });

        bsToast.show();
        return toastId;
    }

    /**
     * Update loading toast message
     * @param {string} toastId - Toast ID
     * @param {string} message - New message
     */
    updateLoading(toastId, message) {
        const toast = this.toasts.get(toastId);
        if (toast && toast.type === 'loading') {
            const bodyElement = toast.element.querySelector('.toast-body');
            if (bodyElement) {
                bodyElement.textContent = message;
            }
        }
    }

    /**
     * Show progress toast
     * @param {string} message - Progress message
     * @param {number} progress - Progress percentage (0-100)
     * @returns {string} - Toast ID
     */
    showProgress(message, progress = 0) {
        const toastId = this.generateId();
        const toastElement = document.createElement('div');
        toastElement.id = toastId;
        toastElement.className = 'toast';
        toastElement.setAttribute('role', 'alert');
        toastElement.setAttribute('aria-live', 'polite');
        toastElement.setAttribute('aria-atomic', 'true');

        toastElement.innerHTML = `
            <div class="toast-header">
                <i class="bi bi-arrow-down-circle text-info me-2"></i>
                <strong class="me-auto">Progress</strong>
                <small class="text-muted">${Math.round(progress)}%</small>
            </div>
            <div class="toast-body">
                <div class="mb-2">${message}</div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar" role="progressbar" style="width: ${progress}%" 
                         aria-valuenow="${progress}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        `;

        this.container.appendChild(toastElement);

        const bsToast = new bootstrap.Toast(toastElement, {
            autohide: false
        });

        this.toasts.set(toastId, {
            element: toastElement,
            bootstrap: bsToast,
            type: 'progress',
            timestamp: Date.now()
        });

        bsToast.show();
        return toastId;
    }

    /**
     * Update progress toast
     * @param {string} toastId - Toast ID
     * @param {string} message - Progress message
     * @param {number} progress - Progress percentage (0-100)
     */
    updateProgress(toastId, message, progress) {
        const toast = this.toasts.get(toastId);
        if (toast && toast.type === 'progress') {
            const bodyElement = toast.element.querySelector('.toast-body div:first-child');
            const progressBar = toast.element.querySelector('.progress-bar');
            const percentageElement = toast.element.querySelector('.toast-header small');

            if (bodyElement) bodyElement.textContent = message;
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
            }
            if (percentageElement) percentageElement.textContent = `${Math.round(progress)}%`;

            // Auto-remove when complete
            if (progress >= 100) {
                setTimeout(() => {
                    this.remove(toastId);
                }, 2000);
            }
        }
    }

    /**
     * Get active toast count
     * @returns {number} - Number of active toasts
     */
    getActiveCount() {
        return this.toasts.size;
    }

    /**
     * Check if toast exists
     * @param {string} toastId - Toast ID
     * @returns {boolean} - Whether toast exists
     */
    exists(toastId) {
        return this.toasts.has(toastId);
    }
}

// Export for use in other modules
window.ToastManager = ToastManager;
