/**
 * Configuration file for WIDDX AI Frontend
 * Contains all application settings and constants
 */
window.WiddxConfig = {
    // Application Information
    app: {
        name: 'WIDDX AI',
        version: '2.0.0',
        description: 'Next-Generation Intelligent Assistant',
        author: 'WIDDX AI Team',
        website: 'https://widdx.ai'
    },

    // API Configuration
    api: {
        baseUrl: '/api',
        timeout: 30000, // 30 seconds
        retryAttempts: 3,
        retryDelay: 1000, // 1 second
        endpoints: {
            auth: {
                login: '/auth/login',
                logout: '/auth/logout',
                refresh: '/auth/refresh',
                register: '/auth/register',
                verify: '/v2/auth/verify',
                profile: '/v2/auth/profile',
                changePassword: '/v2/auth/change-password'
            },
            chat: {
                conversations: '/v2/conversations',
                messages: '/v2/conversations/{id}/messages',
                regenerate: '/v2/conversations/{id}/regenerate',
                export: '/v2/conversations/{id}/export',
                search: '/v2/conversations/search'
            },
            personality: {
                available: '/v2/personality/available',
                current: '/v2/personality',
                switch: '/v2/personality/switch',
                memories: '/v2/personality/memories',
                reset: '/v2/personality/reset'
            },
            knowledge: {
                entries: '/v2/knowledge/entries',
                search: '/v2/knowledge/search',
                similar: '/v2/knowledge/similar/{id}',
                optimize: '/v2/knowledge/optimize',
                import: '/v2/knowledge/import',
                export: '/v2/knowledge/export',
                stats: '/v2/knowledge/stats',
                tags: '/v2/knowledge/tags'
            },
            files: {
                upload: '/v2/files/upload',
                list: '/v2/files',
                details: '/v2/files/{id}',
                process: '/v2/files/{id}/process',
                download: '/v2/files/{id}/download',
                preview: '/v2/files/{id}/preview',
                analysis: '/v2/files/{id}/analysis',
                search: '/v2/files/search',
                stats: '/v2/files/statistics'
            },
            admin: {
                dashboard: '/v2/admin/dashboard',
                users: '/v2/admin/users',
                metrics: '/v2/admin/metrics',
                config: '/v2/admin/config',
                logs: '/v2/admin/logs'
            }
        }
    },

    // WebSocket Configuration
    websocket: {
        url: '/ws',
        transports: ['websocket', 'polling'],
        timeout: 10000,
        reconnect: {
            maxAttempts: 5,
            delay: 1000,
            maxDelay: 30000
        },
        heartbeat: {
            interval: 30000,
            timeout: 5000
        }
    },

    // Storage Configuration
    storage: {
        prefix: 'widdx_ai_',
        encryption: {
            enabled: true,
            algorithm: 'AES-GCM'
        },
        indexedDB: {
            name: 'WiddxAI',
            version: 1,
            stores: ['conversations', 'messages', 'files', 'cache']
        },
        cache: {
            maxSize: 100, // MB
            ttl: 300000 // 5 minutes
        }
    },

    // UI Configuration
    ui: {
        theme: {
            default: 'dark',
            available: ['light', 'dark', 'auto'],
            storageKey: 'widdx_ai_theme'
        },
        language: {
            default: 'en',
            fallback: 'en',
            available: ['en', 'ar', 'fr', 'es', 'de'],
            rtlLanguages: ['ar', 'he', 'fa', 'ur'],
            storageKey: 'widdx_ai_language'
        },
        chat: {
            maxMessageLength: 10000,
            typingTimeout: 3000,
            messageLoadLimit: 50,
            autoScroll: true,
            showTimestamps: true,
            showReasoningByDefault: false
        },
        conversations: {
            maxTitleLength: 100,
            previewLength: 60,
            loadLimit: 20,
            searchDebounce: 300
        },
        notifications: {
            maxToasts: 5,
            defaultDuration: 5000,
            position: 'bottom-end',
            showProgress: true
        }
    },

    // File Upload Configuration
    files: {
        maxSize: 10 * 1024 * 1024, // 10MB
        maxFiles: 5,
        allowedTypes: [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'text/markdown',
            'text/csv',
            'application/json',
            'application/xml',
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'audio/mpeg',
            'audio/wav',
            'audio/ogg',
            'audio/m4a'
        ],
        uploadChunkSize: 1024 * 1024, // 1MB chunks
        previewTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        processingTimeout: 60000 // 1 minute
    },

    // Security Configuration
    security: {
        tokenRefreshThreshold: 5 * 60 * 1000, // 5 minutes
        sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 minutes
        csrfProtection: true,
        contentSecurityPolicy: true
    },

    // Performance Configuration
    performance: {
        debounceDelay: 300,
        throttleDelay: 100,
        lazyLoadThreshold: 100,
        virtualScrollThreshold: 1000,
        cacheStrategy: 'lru', // least recently used
        preloadConversations: 5,
        messageRenderBatch: 20
    },

    // Feature Flags
    features: {
        realtime: true,
        voiceInput: false, // Not implemented yet
        videoCall: false, // Not implemented yet
        screenShare: false, // Not implemented yet
        collaboration: false, // Not implemented yet
        plugins: false, // Not implemented yet
        customThemes: true,
        exportConversations: true,
        knowledgeBase: true,
        personalityCustomization: true,
        adminPanel: true,
        analytics: true,
        offlineMode: false // Not implemented yet
    },

    // Development Configuration
    development: {
        debug: false,
        verbose: false,
        mockData: false,
        skipAuth: false,
        logLevel: 'warn', // error, warn, info, debug
        enableDevTools: false,
        hotReload: false
    },

    // Analytics Configuration
    analytics: {
        enabled: false,
        provider: null, // 'google', 'mixpanel', etc.
        trackingId: null,
        events: {
            pageView: true,
            userActions: true,
            errors: true,
            performance: true
        },
        privacy: {
            anonymizeIp: true,
            respectDoNotTrack: true,
            cookieConsent: true
        }
    },

    // Accessibility Configuration
    accessibility: {
        highContrast: false,
        reducedMotion: false,
        screenReader: true,
        keyboardNavigation: true,
        focusIndicators: true,
        altText: true,
        ariaLabels: true
    },

    // Experimental Features
    experimental: {
        webGL: false,
        webAssembly: false,
        serviceWorker: false,
        webRTC: false,
        webXR: false,
        ai: {
            localProcessing: false,
            edgeComputing: false,
            federatedLearning: false
        }
    },

    // Error Handling
    errorHandling: {
        showStackTrace: false,
        reportErrors: true,
        maxErrorReports: 10,
        errorReportingUrl: null,
        fallbackMode: true,
        gracefulDegradation: true
    },

    // Keyboard Shortcuts
    shortcuts: {
        enabled: true,
        newConversation: 'Ctrl+N',
        focusInput: 'Ctrl+/',
        toggleSidebar: 'Ctrl+B',
        toggleTheme: 'Ctrl+Shift+T',
        search: 'Ctrl+K',
        settings: 'Ctrl+,',
        help: 'F1'
    },

    // Responsive Breakpoints
    breakpoints: {
        xs: 475,
        sm: 640,
        md: 768,
        lg: 1024,
        xl: 1280,
        xxl: 1536
    },

    // Animation Configuration
    animations: {
        enabled: true,
        duration: {
            fast: 150,
            normal: 300,
            slow: 500
        },
        easing: {
            ease: 'ease',
            easeIn: 'ease-in',
            easeOut: 'ease-out',
            easeInOut: 'ease-in-out'
        },
        reducedMotion: false
    },

    // Validation Rules
    validation: {
        email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
        password: {
            minLength: 8,
            requireUppercase: true,
            requireLowercase: true,
            requireNumbers: true,
            requireSpecialChars: false
        },
        username: {
            minLength: 3,
            maxLength: 30,
            allowedChars: /^[a-zA-Z0-9_-]+$/
        },
        conversationTitle: {
            minLength: 1,
            maxLength: 100
        },
        messageContent: {
            minLength: 1,
            maxLength: 10000
        }
    }
};

// Environment-specific overrides
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    // Development environment
    WiddxConfig.development.debug = true;
    WiddxConfig.development.verbose = true;
    WiddxConfig.api.baseUrl = 'http://localhost:8000/api';
} else if (window.location.hostname.includes('staging')) {
    // Staging environment
    WiddxConfig.api.baseUrl = 'https://staging-api.widdx.ai/api';
    WiddxConfig.development.debug = true;
} else {
    // Production environment
    WiddxConfig.api.baseUrl = 'https://api.widdx.ai/api';
    WiddxConfig.analytics.enabled = true;
}

// Browser capability detection
WiddxConfig.capabilities = {
    webSocket: typeof WebSocket !== 'undefined',
    indexedDB: typeof indexedDB !== 'undefined',
    localStorage: typeof localStorage !== 'undefined',
    sessionStorage: typeof sessionStorage !== 'undefined',
    webWorkers: typeof Worker !== 'undefined',
    notifications: 'Notification' in window,
    geolocation: 'geolocation' in navigator,
    camera: 'mediaDevices' in navigator,
    microphone: 'mediaDevices' in navigator,
    clipboard: 'clipboard' in navigator,
    share: 'share' in navigator,
    fullscreen: 'requestFullscreen' in document.documentElement,
    touchScreen: 'ontouchstart' in window,
    darkMode: window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches,
    reducedMotion: window.matchMedia && window.matchMedia('(prefers-reduced-motion: reduce)').matches
};

// Freeze configuration to prevent accidental modifications
Object.freeze(WiddxConfig);

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = WiddxConfig;
}
