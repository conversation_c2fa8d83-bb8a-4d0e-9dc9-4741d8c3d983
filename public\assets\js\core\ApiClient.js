/**
 * ApiClient - HTTP client for WIDDX AI backend communication
 * Handles authentication, request/response processing, and error handling
 */
class ApiClient extends EventEmitter {
    constructor(baseURL = '/api') {
        super();
        this.baseURL = baseURL;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        };
        this.interceptors = {
            request: [],
            response: []
        };
        this.timeout = 30000; // 30 seconds
        this.retryAttempts = 3;
        this.retryDelay = 1000; // 1 second
    }

    /**
     * Set authentication token
     * @param {string} token - JWT token
     */
    setAuthToken(token) {
        if (token) {
            this.defaultHeaders['Authorization'] = `Bearer ${token}`;
        } else {
            delete this.defaultHeaders['Authorization'];
        }
        this.emit('auth:token-updated', token);
    }

    /**
     * Add request interceptor
     * @param {Function} interceptor - Function to modify request config
     */
    addRequestInterceptor(interceptor) {
        this.interceptors.request.push(interceptor);
    }

    /**
     * Add response interceptor
     * @param {Function} interceptor - Function to modify response
     */
    addResponseInterceptor(interceptor) {
        this.interceptors.response.push(interceptor);
    }

    /**
     * Create request configuration
     * @param {string} method - HTTP method
     * @param {string} url - Request URL
     * @param {Object} options - Request options
     * @returns {Object} - Request configuration
     */
    createRequestConfig(method, url, options = {}) {
        const config = {
            method: method.toUpperCase(),
            url: this.resolveURL(url),
            headers: { ...this.defaultHeaders, ...options.headers },
            timeout: options.timeout || this.timeout,
            ...options
        };

        // Apply request interceptors
        return this.interceptors.request.reduce((config, interceptor) => {
            return interceptor(config) || config;
        }, config);
    }

    /**
     * Resolve URL with base URL
     * @param {string} url - Relative or absolute URL
     * @returns {string} - Resolved URL
     */
    resolveURL(url) {
        if (url.startsWith('http://') || url.startsWith('https://')) {
            return url;
        }
        return `${this.baseURL}${url.startsWith('/') ? '' : '/'}${url}`;
    }

    /**
     * Make HTTP request with retry logic
     * @param {Object} config - Request configuration
     * @param {number} attempt - Current attempt number
     * @returns {Promise} - Response promise
     */
    async makeRequest(config, attempt = 1) {
        try {
            this.emit('request:start', config);

            const response = await this.executeRequest(config);
            const processedResponse = this.processResponse(response);

            this.emit('request:success', processedResponse);
            return processedResponse;

        } catch (error) {
            this.emit('request:error', error, config);

            // Retry logic for specific error types
            if (this.shouldRetry(error, attempt)) {
                await this.delay(this.retryDelay * attempt);
                return this.makeRequest(config, attempt + 1);
            }

            throw this.processError(error);
        }
    }

    /**
     * Execute the actual HTTP request
     * @param {Object} config - Request configuration
     * @returns {Promise} - Fetch response
     */
    async executeRequest(config) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), config.timeout);

        try {
            const fetchOptions = {
                method: config.method,
                headers: config.headers,
                signal: controller.signal
            };

            // Add body for non-GET requests
            if (config.method !== 'GET' && config.data) {
                if (config.data instanceof FormData) {
                    fetchOptions.body = config.data;
                    // Remove Content-Type header for FormData (browser sets it)
                    delete fetchOptions.headers['Content-Type'];
                } else {
                    fetchOptions.body = JSON.stringify(config.data);
                }
            }

            // Add query parameters for GET requests
            if (config.method === 'GET' && config.params) {
                const url = new URL(config.url, window.location.origin);
                Object.entries(config.params).forEach(([key, value]) => {
                    if (value !== null && value !== undefined) {
                        url.searchParams.append(key, value);
                    }
                });
                config.url = url.toString();
            }

            const response = await fetch(config.url, fetchOptions);
            clearTimeout(timeoutId);

            return response;

        } catch (error) {
            clearTimeout(timeoutId);
            throw error;
        }
    }

    /**
     * Process successful response
     * @param {Response} response - Fetch response
     * @returns {Object} - Processed response
     */
    async processResponse(response) {
        const result = {
            status: response.status,
            statusText: response.statusText,
            headers: this.parseHeaders(response.headers),
            config: response.config
        };

        // Parse response body
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            result.data = await response.json();
        } else {
            result.data = await response.text();
        }

        // Check for HTTP errors
        if (!response.ok) {
            throw new ApiError(
                result.data?.message || `HTTP ${response.status}: ${response.statusText}`,
                response.status,
                result
            );
        }

        // Apply response interceptors
        return this.interceptors.response.reduce((result, interceptor) => {
            return interceptor(result) || result;
        }, result);
    }

    /**
     * Process request error
     * @param {Error} error - Request error
     * @returns {ApiError} - Processed error
     */
    processError(error) {
        if (error instanceof ApiError) {
            return error;
        }

        if (error.name === 'AbortError') {
            return new ApiError('Request timeout', 408, null, error);
        }

        if (!navigator.onLine) {
            return new ApiError('Network connection lost', 0, null, error);
        }

        return new ApiError(
            error.message || 'Network request failed',
            0,
            null,
            error
        );
    }

    /**
     * Check if request should be retried
     * @param {Error} error - Request error
     * @param {number} attempt - Current attempt number
     * @returns {boolean} - Whether to retry
     */
    shouldRetry(error, attempt) {
        if (attempt >= this.retryAttempts) {
            return false;
        }

        // Retry on network errors or 5xx server errors
        return (
            error.status === 0 || // Network error
            (error.status >= 500 && error.status < 600) || // Server error
            error.name === 'AbortError' // Timeout
        );
    }

    /**
     * Parse response headers
     * @param {Headers} headers - Response headers
     * @returns {Object} - Parsed headers object
     */
    parseHeaders(headers) {
        const result = {};
        for (const [key, value] of headers.entries()) {
            result[key] = value;
        }
        return result;
    }

    /**
     * Delay execution
     * @param {number} ms - Milliseconds to delay
     * @returns {Promise} - Delay promise
     */
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // HTTP method shortcuts
    async get(url, options = {}) {
        const config = this.createRequestConfig('GET', url, options);
        return this.makeRequest(config);
    }

    async post(url, data, options = {}) {
        const config = this.createRequestConfig('POST', url, { ...options, data });
        return this.makeRequest(config);
    }

    async put(url, data, options = {}) {
        const config = this.createRequestConfig('PUT', url, { ...options, data });
        return this.makeRequest(config);
    }

    async patch(url, data, options = {}) {
        const config = this.createRequestConfig('PATCH', url, { ...options, data });
        return this.makeRequest(config);
    }

    async delete(url, options = {}) {
        const config = this.createRequestConfig('DELETE', url, options);
        return this.makeRequest(config);
    }

    /**
     * Upload file with progress tracking
     * @param {string} url - Upload URL
     * @param {FormData} formData - Form data with file
     * @param {Function} onProgress - Progress callback
     * @returns {Promise} - Upload promise
     */
    async upload(url, formData, onProgress) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            // Set up progress tracking
            if (onProgress) {
                xhr.upload.addEventListener('progress', (event) => {
                    if (event.lengthComputable) {
                        const progress = (event.loaded / event.total) * 100;
                        onProgress(progress, event.loaded, event.total);
                    }
                });
            }

            // Set up response handling
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve({ data: response, status: xhr.status });
                    } catch (error) {
                        resolve({ data: xhr.responseText, status: xhr.status });
                    }
                } else {
                    reject(new ApiError(`Upload failed: ${xhr.statusText}`, xhr.status));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new ApiError('Upload failed: Network error', 0));
            });

            xhr.addEventListener('timeout', () => {
                reject(new ApiError('Upload failed: Timeout', 408));
            });

            // Configure and send request
            xhr.open('POST', this.resolveURL(url));
            
            // Add auth header if available
            if (this.defaultHeaders['Authorization']) {
                xhr.setRequestHeader('Authorization', this.defaultHeaders['Authorization']);
            }

            xhr.timeout = this.timeout;
            xhr.send(formData);
        });
    }
}

/**
 * Custom API Error class
 */
class ApiError extends Error {
    constructor(message, status = 0, response = null, originalError = null) {
        super(message);
        this.name = 'ApiError';
        this.status = status;
        this.response = response;
        this.originalError = originalError;
    }

    get isNetworkError() {
        return this.status === 0;
    }

    get isServerError() {
        return this.status >= 500 && this.status < 600;
    }

    get isClientError() {
        return this.status >= 400 && this.status < 500;
    }

    get isAuthError() {
        return this.status === 401 || this.status === 403;
    }
}

// Export classes
window.ApiClient = ApiClient;
window.ApiError = ApiError;
