/**
 * EventEmitter - Core event system for WIDDX AI
 * Provides a centralized event management system using Observer pattern
 */
class EventEmitter {
    constructor() {
        this.events = new Map();
        this.maxListeners = 100;
    }

    /**
     * Add an event listener
     * @param {string} event - Event name
     * @param {Function} listener - Event handler function
     * @param {Object} options - Options (once, priority)
     * @returns {EventEmitter} - Returns this for chaining
     */
    on(event, listener, options = {}) {
        if (typeof listener !== 'function') {
            throw new TypeError('Listener must be a function');
        }

        if (!this.events.has(event)) {
            this.events.set(event, []);
        }

        const listeners = this.events.get(event);
        
        // Check max listeners
        if (listeners.length >= this.maxListeners) {
            console.warn(`Max listeners (${this.maxListeners}) exceeded for event: ${event}`);
        }

        const listenerObj = {
            fn: listener,
            once: options.once || false,
            priority: options.priority || 0,
            context: options.context || null
        };

        // Insert based on priority (higher priority first)
        const insertIndex = listeners.findIndex(l => l.priority < listenerObj.priority);
        if (insertIndex === -1) {
            listeners.push(listenerObj);
        } else {
            listeners.splice(insertIndex, 0, listenerObj);
        }

        return this;
    }

    /**
     * Add a one-time event listener
     * @param {string} event - Event name
     * @param {Function} listener - Event handler function
     * @param {Object} options - Additional options
     * @returns {EventEmitter} - Returns this for chaining
     */
    once(event, listener, options = {}) {
        return this.on(event, listener, { ...options, once: true });
    }

    /**
     * Remove an event listener
     * @param {string} event - Event name
     * @param {Function} listener - Event handler function to remove
     * @returns {EventEmitter} - Returns this for chaining
     */
    off(event, listener) {
        if (!this.events.has(event)) {
            return this;
        }

        const listeners = this.events.get(event);
        const index = listeners.findIndex(l => l.fn === listener);
        
        if (index !== -1) {
            listeners.splice(index, 1);
        }

        // Clean up empty event arrays
        if (listeners.length === 0) {
            this.events.delete(event);
        }

        return this;
    }

    /**
     * Remove all listeners for an event or all events
     * @param {string} [event] - Event name (optional)
     * @returns {EventEmitter} - Returns this for chaining
     */
    removeAllListeners(event) {
        if (event) {
            this.events.delete(event);
        } else {
            this.events.clear();
        }
        return this;
    }

    /**
     * Emit an event
     * @param {string} event - Event name
     * @param {...any} args - Arguments to pass to listeners
     * @returns {boolean} - Returns true if event had listeners
     */
    emit(event, ...args) {
        if (!this.events.has(event)) {
            return false;
        }

        const listeners = [...this.events.get(event)]; // Copy to avoid modification during iteration
        let hasListeners = false;

        for (const listener of listeners) {
            hasListeners = true;
            
            try {
                if (listener.context) {
                    listener.fn.call(listener.context, ...args);
                } else {
                    listener.fn(...args);
                }
            } catch (error) {
                console.error(`Error in event listener for '${event}':`, error);
                this.emit('error', error, event);
            }

            // Remove one-time listeners
            if (listener.once) {
                this.off(event, listener.fn);
            }
        }

        return hasListeners;
    }

    /**
     * Emit an event asynchronously
     * @param {string} event - Event name
     * @param {...any} args - Arguments to pass to listeners
     * @returns {Promise<boolean>} - Promise that resolves to true if event had listeners
     */
    async emitAsync(event, ...args) {
        if (!this.events.has(event)) {
            return false;
        }

        const listeners = [...this.events.get(event)];
        let hasListeners = false;

        for (const listener of listeners) {
            hasListeners = true;
            
            try {
                const result = listener.context 
                    ? listener.fn.call(listener.context, ...args)
                    : listener.fn(...args);
                
                // Handle async listeners
                if (result instanceof Promise) {
                    await result;
                }
            } catch (error) {
                console.error(`Error in async event listener for '${event}':`, error);
                this.emit('error', error, event);
            }

            // Remove one-time listeners
            if (listener.once) {
                this.off(event, listener.fn);
            }
        }

        return hasListeners;
    }

    /**
     * Get the number of listeners for an event
     * @param {string} event - Event name
     * @returns {number} - Number of listeners
     */
    listenerCount(event) {
        return this.events.has(event) ? this.events.get(event).length : 0;
    }

    /**
     * Get all event names that have listeners
     * @returns {string[]} - Array of event names
     */
    eventNames() {
        return Array.from(this.events.keys());
    }

    /**
     * Get all listeners for an event
     * @param {string} event - Event name
     * @returns {Function[]} - Array of listener functions
     */
    listeners(event) {
        if (!this.events.has(event)) {
            return [];
        }
        return this.events.get(event).map(l => l.fn);
    }

    /**
     * Set the maximum number of listeners per event
     * @param {number} max - Maximum number of listeners
     * @returns {EventEmitter} - Returns this for chaining
     */
    setMaxListeners(max) {
        if (typeof max !== 'number' || max < 0) {
            throw new TypeError('Max listeners must be a non-negative number');
        }
        this.maxListeners = max;
        return this;
    }

    /**
     * Create a promise that resolves when an event is emitted
     * @param {string} event - Event name
     * @param {number} [timeout] - Optional timeout in milliseconds
     * @returns {Promise} - Promise that resolves with event arguments
     */
    waitFor(event, timeout) {
        return new Promise((resolve, reject) => {
            let timeoutId;

            const listener = (...args) => {
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                resolve(args);
            };

            this.once(event, listener);

            if (timeout) {
                timeoutId = setTimeout(() => {
                    this.off(event, listener);
                    reject(new Error(`Timeout waiting for event: ${event}`));
                }, timeout);
            }
        });
    }

    /**
     * Pipe events from this emitter to another emitter
     * @param {EventEmitter} target - Target emitter
     * @param {string|string[]} events - Event name(s) to pipe
     * @returns {EventEmitter} - Returns this for chaining
     */
    pipe(target, events) {
        if (!(target instanceof EventEmitter)) {
            throw new TypeError('Target must be an EventEmitter instance');
        }

        const eventList = Array.isArray(events) ? events : [events];

        eventList.forEach(event => {
            this.on(event, (...args) => {
                target.emit(event, ...args);
            });
        });

        return this;
    }
}

// Export for use in other modules
window.EventEmitter = EventEmitter;
