/**
 * I18nManager - Internationalization manager for WIDDX AI
 * Handles multi-language support with RTL/LTR and dynamic loading
 */
class I18nManager extends EventEmitter {
    constructor() {
        super();
        this.currentLanguage = 'en';
        this.fallbackLanguage = 'en';
        this.translations = new Map();
        this.rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        this.loadedLanguages = new Set();
        this.interpolationRegex = /\{\{([^}]+)\}\}/g;
        
        this.initializeLanguages();
    }

    /**
     * Initialize language system
     */
    async initializeLanguages() {
        // Load default translations
        await this.loadLanguageData();
        
        // Detect user's preferred language
        const savedLanguage = localStorage.getItem('widdx_ai_language');
        const browserLanguage = navigator.language.split('-')[0];
        const preferredLanguage = savedLanguage || browserLanguage || 'en';
        
        await this.setLanguage(preferredLanguage);
        this.emit('i18n:initialized', this.currentLanguage);
    }

    /**
     * Load language data (embedded for demo, would be loaded from server)
     */
    async loadLanguageData() {
        const languages = {
            en: {
                // Common
                'common.loading': 'Loading...',
                'common.error': 'Error',
                'common.success': 'Success',
                'common.cancel': 'Cancel',
                'common.save': 'Save',
                'common.delete': 'Delete',
                'common.edit': 'Edit',
                'common.search': 'Search',
                'common.filter': 'Filter',
                'common.export': 'Export',
                'common.import': 'Import',
                'common.settings': 'Settings',
                'common.help': 'Help',
                'common.close': 'Close',
                'common.confirm': 'Confirm',
                
                // Navigation
                'nav.dashboard': 'Dashboard',
                'nav.chat': 'Chat',
                'nav.knowledge': 'Knowledge Base',
                'nav.personality': 'Personality',
                'nav.admin': 'Admin Panel',
                'nav.logout': 'Logout',
                
                // Chat
                'chat.title': 'Chat',
                'chat.newConversation': 'New Conversation',
                'chat.recentConversations': 'Recent Conversations',
                'chat.sendMessage': 'Send message',
                'chat.messagePlaceholder': 'Type your message here...',
                'chat.typing': 'AI is typing...',
                'chat.regenerate': 'Regenerate response',
                'chat.copy': 'Copy message',
                'chat.reasoning': 'Show reasoning',
                'chat.attachFile': 'Attach file',
                'chat.voiceInput': 'Voice input',
                'chat.welcomeMessage': 'Welcome to WIDDX AI! I\'m your intelligent assistant powered by multiple AI models. I can help you with coding, research, creative writing, and much more. How can I assist you today?',
                'chat.emptyState': 'No conversations yet. Start a new conversation to begin!',
                'chat.exportConversation': 'Export Conversation',
                'chat.clearConversation': 'Clear Conversation',
                
                // Personality
                'personality.title': 'AI Personality',
                'personality.current': 'Current personality',
                'personality.switch': 'Switch personality',
                'personality.customize': 'Customize',
                'personality.memories': 'Memories',
                'personality.preferences': 'Preferences',
                'personality.traits': 'Personality Traits',
                'personality.tone': 'Communication Tone',
                'personality.verbosity': 'Response Length',
                'personality.creativity': 'Creativity Level',
                
                // Knowledge Base
                'knowledge.title': 'Knowledge Base',
                'knowledge.search': 'Search knowledge',
                'knowledge.addEntry': 'Add entry',
                'knowledge.upload': 'Upload file',
                'knowledge.verified': 'Verified',
                'knowledge.confidence': 'Confidence',
                'knowledge.usage': 'Usage count',
                'knowledge.source': 'Source',
                'knowledge.tags': 'Tags',
                'knowledge.similar': 'Similar entries',
                'knowledge.optimize': 'Optimize database',
                
                // File Upload
                'file.upload': 'Upload Files',
                'file.dragDrop': 'Drag and drop files here or click to browse',
                'file.processing': 'Processing file...',
                'file.processed': 'File processed successfully',
                'file.error': 'File processing failed',
                'file.maxSize': 'Maximum file size: 10MB',
                'file.supportedTypes': 'Supported types: PDF, DOCX, TXT, JPG, PNG, MP3, WAV, CSV, JSON',
                
                // Admin
                'admin.dashboard': 'Admin Dashboard',
                'admin.users': 'Users',
                'admin.metrics': 'System Metrics',
                'admin.config': 'Configuration',
                'admin.logs': 'System Logs',
                'admin.performance': 'Performance',
                'admin.models': 'AI Models',
                'admin.backup': 'Backup & Restore',
                
                // Status
                'status.online': 'Online',
                'status.offline': 'Offline',
                'status.connecting': 'Connecting...',
                'status.ready': 'Ready',
                'status.processing': 'Processing...',
                
                // Errors
                'error.network': 'Network connection error',
                'error.server': 'Server error occurred',
                'error.auth': 'Authentication failed',
                'error.permission': 'Permission denied',
                'error.notFound': 'Resource not found',
                'error.validation': 'Validation error',
                'error.timeout': 'Request timeout',
                'error.unknown': 'An unknown error occurred'
            },
            
            ar: {
                // Common
                'common.loading': 'جاري التحميل...',
                'common.error': 'خطأ',
                'common.success': 'نجح',
                'common.cancel': 'إلغاء',
                'common.save': 'حفظ',
                'common.delete': 'حذف',
                'common.edit': 'تعديل',
                'common.search': 'بحث',
                'common.filter': 'تصفية',
                'common.export': 'تصدير',
                'common.import': 'استيراد',
                'common.settings': 'الإعدادات',
                'common.help': 'مساعدة',
                'common.close': 'إغلاق',
                'common.confirm': 'تأكيد',
                
                // Navigation
                'nav.dashboard': 'لوحة التحكم',
                'nav.chat': 'المحادثة',
                'nav.knowledge': 'قاعدة المعرفة',
                'nav.personality': 'الشخصية',
                'nav.admin': 'لوحة الإدارة',
                'nav.logout': 'تسجيل الخروج',
                
                // Chat
                'chat.title': 'المحادثة',
                'chat.newConversation': 'محادثة جديدة',
                'chat.recentConversations': 'المحادثات الأخيرة',
                'chat.sendMessage': 'إرسال رسالة',
                'chat.messagePlaceholder': 'اكتب رسالتك هنا...',
                'chat.typing': 'الذكي الاصطناعي يكتب...',
                'chat.regenerate': 'إعادة توليد الرد',
                'chat.copy': 'نسخ الرسالة',
                'chat.reasoning': 'إظهار التفكير',
                'chat.attachFile': 'إرفاق ملف',
                'chat.voiceInput': 'إدخال صوتي',
                'chat.welcomeMessage': 'مرحباً بك في WIDDX AI! أنا مساعدك الذكي المدعوم بنماذج ذكاء اصطناعي متعددة. يمكنني مساعدتك في البرمجة والبحث والكتابة الإبداعية وأكثر من ذلك بكثير. كيف يمكنني مساعدتك اليوم؟',
                'chat.emptyState': 'لا توجد محادثات بعد. ابدأ محادثة جديدة للبدء!',
                'chat.exportConversation': 'تصدير المحادثة',
                'chat.clearConversation': 'مسح المحادثة',
                
                // Personality
                'personality.title': 'شخصية الذكاء الاصطناعي',
                'personality.current': 'الشخصية الحالية',
                'personality.switch': 'تغيير الشخصية',
                'personality.customize': 'تخصيص',
                'personality.memories': 'الذكريات',
                'personality.preferences': 'التفضيلات',
                'personality.traits': 'سمات الشخصية',
                'personality.tone': 'نبرة التواصل',
                'personality.verbosity': 'طول الرد',
                'personality.creativity': 'مستوى الإبداع',
                
                // Knowledge Base
                'knowledge.title': 'قاعدة المعرفة',
                'knowledge.search': 'البحث في المعرفة',
                'knowledge.addEntry': 'إضافة مدخل',
                'knowledge.upload': 'رفع ملف',
                'knowledge.verified': 'موثق',
                'knowledge.confidence': 'الثقة',
                'knowledge.usage': 'عدد الاستخدامات',
                'knowledge.source': 'المصدر',
                'knowledge.tags': 'العلامات',
                'knowledge.similar': 'مدخلات مشابهة',
                'knowledge.optimize': 'تحسين قاعدة البيانات',
                
                // Status
                'status.online': 'متصل',
                'status.offline': 'غير متصل',
                'status.connecting': 'جاري الاتصال...',
                'status.ready': 'جاهز',
                'status.processing': 'جاري المعالجة...',
                
                // Errors
                'error.network': 'خطأ في الاتصال بالشبكة',
                'error.server': 'حدث خطأ في الخادم',
                'error.auth': 'فشل في المصادقة',
                'error.permission': 'تم رفض الإذن',
                'error.notFound': 'المورد غير موجود',
                'error.validation': 'خطأ في التحقق',
                'error.timeout': 'انتهت مهلة الطلب',
                'error.unknown': 'حدث خطأ غير معروف'
            }
        };

        // Store translations
        for (const [lang, translations] of Object.entries(languages)) {
            this.translations.set(lang, translations);
            this.loadedLanguages.add(lang);
        }
    }

    /**
     * Set current language
     * @param {string} language - Language code
     */
    async setLanguage(language) {
        if (!this.loadedLanguages.has(language)) {
            try {
                await this.loadLanguage(language);
            } catch (error) {
                console.warn(`Failed to load language ${language}, using fallback`);
                language = this.fallbackLanguage;
            }
        }

        const previousLanguage = this.currentLanguage;
        this.currentLanguage = language;

        // Update document attributes
        document.documentElement.lang = language;
        document.documentElement.dir = this.isRTL(language) ? 'rtl' : 'ltr';

        // Save preference
        localStorage.setItem('widdx_ai_language', language);

        // Update UI
        this.updateUI();

        this.emit('i18n:language-changed', {
            current: language,
            previous: previousLanguage,
            isRTL: this.isRTL(language)
        });
    }

    /**
     * Load language from server (placeholder)
     * @param {string} language - Language code
     */
    async loadLanguage(language) {
        // In a real application, this would fetch from server
        // For demo, we'll just mark as loaded if it's a supported language
        const supportedLanguages = ['en', 'ar', 'fr', 'es', 'de'];
        
        if (supportedLanguages.includes(language)) {
            this.loadedLanguages.add(language);
            return true;
        }
        
        throw new Error(`Language ${language} not supported`);
    }

    /**
     * Check if language is RTL
     * @param {string} language - Language code
     * @returns {boolean} - True if RTL
     */
    isRTL(language = this.currentLanguage) {
        return this.rtlLanguages.includes(language);
    }

    /**
     * Get translation for a key
     * @param {string} key - Translation key
     * @param {Object} params - Interpolation parameters
     * @param {string} language - Language override
     * @returns {string} - Translated text
     */
    t(key, params = {}, language = this.currentLanguage) {
        const translations = this.translations.get(language) || this.translations.get(this.fallbackLanguage);
        
        if (!translations) {
            console.warn(`No translations found for language: ${language}`);
            return key;
        }

        let translation = translations[key];
        
        if (!translation) {
            // Try fallback language
            const fallbackTranslations = this.translations.get(this.fallbackLanguage);
            translation = fallbackTranslations?.[key];
            
            if (!translation) {
                console.warn(`Translation not found for key: ${key}`);
                return key;
            }
        }

        // Interpolate parameters
        return this.interpolate(translation, params);
    }

    /**
     * Interpolate parameters in translation string
     * @param {string} text - Text with placeholders
     * @param {Object} params - Parameters to interpolate
     * @returns {string} - Interpolated text
     */
    interpolate(text, params) {
        return text.replace(this.interpolationRegex, (match, key) => {
            const value = params[key.trim()];
            return value !== undefined ? value : match;
        });
    }

    /**
     * Update UI with current language
     */
    updateUI() {
        // Update elements with data-i18n attribute
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                element.placeholder = translation;
            } else {
                element.textContent = translation;
            }
        });

        // Update elements with data-i18n-placeholder attribute
        const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            element.placeholder = this.t(key);
        });

        // Update elements with data-i18n-title attribute
        const titleElements = document.querySelectorAll('[data-i18n-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            element.title = this.t(key);
        });

        // Update current language display
        const languageDisplay = document.getElementById('current-language');
        if (languageDisplay) {
            const languageNames = {
                en: 'EN',
                ar: 'ع',
                fr: 'FR',
                es: 'ES',
                de: 'DE'
            };
            languageDisplay.textContent = languageNames[this.currentLanguage] || this.currentLanguage.toUpperCase();
        }

        this.emit('i18n:ui-updated', this.currentLanguage);
    }

    /**
     * Get available languages
     * @returns {Array} - Array of language objects
     */
    getAvailableLanguages() {
        return [
            { code: 'en', name: 'English', nativeName: 'English', flag: '🇺🇸' },
            { code: 'ar', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦' },
            { code: 'fr', name: 'French', nativeName: 'Français', flag: '🇫🇷' },
            { code: 'es', name: 'Spanish', nativeName: 'Español', flag: '🇪🇸' },
            { code: 'de', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪' }
        ];
    }

    /**
     * Get current language info
     * @returns {Object} - Current language information
     */
    getCurrentLanguageInfo() {
        const languages = this.getAvailableLanguages();
        return languages.find(lang => lang.code === this.currentLanguage) || languages[0];
    }

    /**
     * Format number according to current locale
     * @param {number} number - Number to format
     * @param {Object} options - Formatting options
     * @returns {string} - Formatted number
     */
    formatNumber(number, options = {}) {
        try {
            return new Intl.NumberFormat(this.currentLanguage, options).format(number);
        } catch (error) {
            return number.toString();
        }
    }

    /**
     * Format date according to current locale
     * @param {Date} date - Date to format
     * @param {Object} options - Formatting options
     * @returns {string} - Formatted date
     */
    formatDate(date, options = {}) {
        try {
            return new Intl.DateTimeFormat(this.currentLanguage, options).format(date);
        } catch (error) {
            return date.toString();
        }
    }

    /**
     * Format relative time (e.g., "2 hours ago")
     * @param {Date} date - Date to format
     * @returns {string} - Relative time string
     */
    formatRelativeTime(date) {
        try {
            const rtf = new Intl.RelativeTimeFormat(this.currentLanguage, { numeric: 'auto' });
            const diff = date.getTime() - Date.now();
            const diffInSeconds = Math.floor(diff / 1000);
            const diffInMinutes = Math.floor(diffInSeconds / 60);
            const diffInHours = Math.floor(diffInMinutes / 60);
            const diffInDays = Math.floor(diffInHours / 24);

            if (Math.abs(diffInDays) > 0) {
                return rtf.format(diffInDays, 'day');
            } else if (Math.abs(diffInHours) > 0) {
                return rtf.format(diffInHours, 'hour');
            } else if (Math.abs(diffInMinutes) > 0) {
                return rtf.format(diffInMinutes, 'minute');
            } else {
                return rtf.format(diffInSeconds, 'second');
            }
        } catch (error) {
            return date.toLocaleString();
        }
    }
}

// Export for use in other modules
window.I18nManager = I18nManager;
