/**
 * StorageManager - Unified storage management for WIDDX AI
 * Handles localStorage, sessionStorage, and IndexedDB with encryption support
 */
class StorageManager extends EventEmitter {
    constructor() {
        super();
        this.prefix = 'widdx_ai_';
        this.encryptionKey = null;
        this.compressionEnabled = true;
        this.dbName = 'WiddxAI';
        this.dbVersion = 1;
        this.db = null;
        
        this.initializeIndexedDB();
    }

    /**
     * Initialize IndexedDB for large data storage
     */
    async initializeIndexedDB() {
        try {
            this.db = await this.openIndexedDB();
            this.emit('storage:db-ready');
        } catch (error) {
            console.warn('IndexedDB initialization failed:', error);
            this.emit('storage:db-error', error);
        }
    }

    /**
     * Open IndexedDB connection
     * @returns {Promise<IDBDatabase>} - Database instance
     */
    openIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => resolve(request.result);

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Create object stores
                if (!db.objectStoreNames.contains('conversations')) {
                    const conversationStore = db.createObjectStore('conversations', { keyPath: 'id' });
                    conversationStore.createIndex('userId', 'userId', { unique: false });
                    conversationStore.createIndex('lastActivity', 'lastActivityAt', { unique: false });
                }

                if (!db.objectStoreNames.contains('messages')) {
                    const messageStore = db.createObjectStore('messages', { keyPath: 'id' });
                    messageStore.createIndex('conversationId', 'conversationId', { unique: false });
                    messageStore.createIndex('timestamp', 'timestamp', { unique: false });
                }

                if (!db.objectStoreNames.contains('files')) {
                    const fileStore = db.createObjectStore('files', { keyPath: 'id' });
                    fileStore.createIndex('type', 'type', { unique: false });
                    fileStore.createIndex('uploadDate', 'uploadDate', { unique: false });
                }

                if (!db.objectStoreNames.contains('cache')) {
                    const cacheStore = db.createObjectStore('cache', { keyPath: 'key' });
                    cacheStore.createIndex('expiry', 'expiry', { unique: false });
                }
            };
        });
    }

    /**
     * Set encryption key for sensitive data
     * @param {string} key - Encryption key
     */
    setEncryptionKey(key) {
        this.encryptionKey = key;
        this.emit('storage:encryption-enabled');
    }

    /**
     * Generate storage key with prefix
     * @param {string} key - Original key
     * @returns {string} - Prefixed key
     */
    generateKey(key) {
        return `${this.prefix}${key}`;
    }

    /**
     * Compress data using simple compression
     * @param {string} data - Data to compress
     * @returns {string} - Compressed data
     */
    compress(data) {
        if (!this.compressionEnabled) return data;
        
        try {
            // Simple LZ-string compression (would need library in production)
            return btoa(data);
        } catch (error) {
            console.warn('Compression failed:', error);
            return data;
        }
    }

    /**
     * Decompress data
     * @param {string} data - Compressed data
     * @returns {string} - Decompressed data
     */
    decompress(data) {
        if (!this.compressionEnabled) return data;
        
        try {
            return atob(data);
        } catch (error) {
            console.warn('Decompression failed:', error);
            return data;
        }
    }

    /**
     * Encrypt sensitive data
     * @param {string} data - Data to encrypt
     * @returns {string} - Encrypted data
     */
    encrypt(data) {
        if (!this.encryptionKey) return data;
        
        try {
            // Simple XOR encryption (use proper encryption in production)
            let encrypted = '';
            for (let i = 0; i < data.length; i++) {
                encrypted += String.fromCharCode(
                    data.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
                );
            }
            return btoa(encrypted);
        } catch (error) {
            console.warn('Encryption failed:', error);
            return data;
        }
    }

    /**
     * Decrypt sensitive data
     * @param {string} data - Encrypted data
     * @returns {string} - Decrypted data
     */
    decrypt(data) {
        if (!this.encryptionKey) return data;
        
        try {
            const encrypted = atob(data);
            let decrypted = '';
            for (let i = 0; i < encrypted.length; i++) {
                decrypted += String.fromCharCode(
                    encrypted.charCodeAt(i) ^ this.encryptionKey.charCodeAt(i % this.encryptionKey.length)
                );
            }
            return decrypted;
        } catch (error) {
            console.warn('Decryption failed:', error);
            return data;
        }
    }

    /**
     * Store data in localStorage
     * @param {string} key - Storage key
     * @param {any} value - Value to store
     * @param {Object} options - Storage options
     */
    setLocal(key, value, options = {}) {
        try {
            const storageKey = this.generateKey(key);
            let data = JSON.stringify({
                value,
                timestamp: Date.now(),
                expiry: options.expiry || null,
                encrypted: options.encrypt || false
            });

            if (options.encrypt) {
                data = this.encrypt(data);
            }

            if (options.compress) {
                data = this.compress(data);
            }

            localStorage.setItem(storageKey, data);
            this.emit('storage:set', { type: 'local', key, value });

        } catch (error) {
            console.error('Failed to set localStorage:', error);
            this.emit('storage:error', { type: 'local', operation: 'set', key, error });
        }
    }

    /**
     * Get data from localStorage
     * @param {string} key - Storage key
     * @param {any} defaultValue - Default value if not found
     * @returns {any} - Stored value or default
     */
    getLocal(key, defaultValue = null) {
        try {
            const storageKey = this.generateKey(key);
            let data = localStorage.getItem(storageKey);

            if (!data) return defaultValue;

            // Try to decompress if needed
            try {
                data = this.decompress(data);
            } catch (e) {
                // Data might not be compressed
            }

            const parsed = JSON.parse(data);

            // Check expiry
            if (parsed.expiry && Date.now() > parsed.expiry) {
                this.removeLocal(key);
                return defaultValue;
            }

            // Decrypt if needed
            if (parsed.encrypted) {
                parsed.value = this.decrypt(parsed.value);
            }

            this.emit('storage:get', { type: 'local', key, value: parsed.value });
            return parsed.value;

        } catch (error) {
            console.error('Failed to get localStorage:', error);
            this.emit('storage:error', { type: 'local', operation: 'get', key, error });
            return defaultValue;
        }
    }

    /**
     * Remove data from localStorage
     * @param {string} key - Storage key
     */
    removeLocal(key) {
        try {
            const storageKey = this.generateKey(key);
            localStorage.removeItem(storageKey);
            this.emit('storage:remove', { type: 'local', key });
        } catch (error) {
            console.error('Failed to remove localStorage:', error);
            this.emit('storage:error', { type: 'local', operation: 'remove', key, error });
        }
    }

    /**
     * Store data in sessionStorage
     * @param {string} key - Storage key
     * @param {any} value - Value to store
     * @param {Object} options - Storage options
     */
    setSession(key, value, options = {}) {
        try {
            const storageKey = this.generateKey(key);
            let data = JSON.stringify({
                value,
                timestamp: Date.now(),
                encrypted: options.encrypt || false
            });

            if (options.encrypt) {
                data = this.encrypt(data);
            }

            sessionStorage.setItem(storageKey, data);
            this.emit('storage:set', { type: 'session', key, value });

        } catch (error) {
            console.error('Failed to set sessionStorage:', error);
            this.emit('storage:error', { type: 'session', operation: 'set', key, error });
        }
    }

    /**
     * Get data from sessionStorage
     * @param {string} key - Storage key
     * @param {any} defaultValue - Default value if not found
     * @returns {any} - Stored value or default
     */
    getSession(key, defaultValue = null) {
        try {
            const storageKey = this.generateKey(key);
            const data = sessionStorage.getItem(storageKey);

            if (!data) return defaultValue;

            const parsed = JSON.parse(data);

            // Decrypt if needed
            if (parsed.encrypted) {
                parsed.value = this.decrypt(parsed.value);
            }

            this.emit('storage:get', { type: 'session', key, value: parsed.value });
            return parsed.value;

        } catch (error) {
            console.error('Failed to get sessionStorage:', error);
            this.emit('storage:error', { type: 'session', operation: 'get', key, error });
            return defaultValue;
        }
    }

    /**
     * Remove data from sessionStorage
     * @param {string} key - Storage key
     */
    removeSession(key) {
        try {
            const storageKey = this.generateKey(key);
            sessionStorage.removeItem(storageKey);
            this.emit('storage:remove', { type: 'session', key });
        } catch (error) {
            console.error('Failed to remove sessionStorage:', error);
            this.emit('storage:error', { type: 'session', operation: 'remove', key, error });
        }
    }

    /**
     * Store data in IndexedDB
     * @param {string} storeName - Object store name
     * @param {any} data - Data to store
     * @returns {Promise} - Storage promise
     */
    async setIndexedDB(storeName, data) {
        if (!this.db) {
            throw new Error('IndexedDB not available');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            request.onsuccess = () => {
                this.emit('storage:set', { type: 'indexeddb', store: storeName, data });
                resolve(request.result);
            };

            request.onerror = () => {
                this.emit('storage:error', { type: 'indexeddb', operation: 'set', store: storeName, error: request.error });
                reject(request.error);
            };
        });
    }

    /**
     * Get data from IndexedDB
     * @param {string} storeName - Object store name
     * @param {any} key - Record key
     * @returns {Promise} - Data promise
     */
    async getIndexedDB(storeName, key) {
        if (!this.db) {
            throw new Error('IndexedDB not available');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(key);

            request.onsuccess = () => {
                this.emit('storage:get', { type: 'indexeddb', store: storeName, key, data: request.result });
                resolve(request.result);
            };

            request.onerror = () => {
                this.emit('storage:error', { type: 'indexeddb', operation: 'get', store: storeName, key, error: request.error });
                reject(request.error);
            };
        });
    }

    /**
     * Clear all storage data
     * @param {boolean} includeIndexedDB - Whether to clear IndexedDB
     */
    async clearAll(includeIndexedDB = false) {
        try {
            // Clear localStorage
            const localKeys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
            localKeys.forEach(key => localStorage.removeItem(key));

            // Clear sessionStorage
            const sessionKeys = Object.keys(sessionStorage).filter(key => key.startsWith(this.prefix));
            sessionKeys.forEach(key => sessionStorage.removeItem(key));

            // Clear IndexedDB if requested
            if (includeIndexedDB && this.db) {
                const storeNames = ['conversations', 'messages', 'files', 'cache'];
                for (const storeName of storeNames) {
                    const transaction = this.db.transaction([storeName], 'readwrite');
                    const store = transaction.objectStore(storeName);
                    await new Promise((resolve, reject) => {
                        const request = store.clear();
                        request.onsuccess = () => resolve();
                        request.onerror = () => reject(request.error);
                    });
                }
            }

            this.emit('storage:cleared');

        } catch (error) {
            console.error('Failed to clear storage:', error);
            this.emit('storage:error', { operation: 'clear', error });
        }
    }

    /**
     * Get storage usage statistics
     * @returns {Object} - Storage usage info
     */
    getStorageInfo() {
        const info = {
            localStorage: {
                used: 0,
                available: false
            },
            sessionStorage: {
                used: 0,
                available: false
            },
            indexedDB: {
                available: !!this.db
            }
        };

        try {
            // Calculate localStorage usage
            let localStorageSize = 0;
            for (const key in localStorage) {
                if (key.startsWith(this.prefix)) {
                    localStorageSize += localStorage[key].length;
                }
            }
            info.localStorage.used = localStorageSize;
            info.localStorage.available = true;
        } catch (error) {
            console.warn('Cannot access localStorage:', error);
        }

        try {
            // Calculate sessionStorage usage
            let sessionStorageSize = 0;
            for (const key in sessionStorage) {
                if (key.startsWith(this.prefix)) {
                    sessionStorageSize += sessionStorage[key].length;
                }
            }
            info.sessionStorage.used = sessionStorageSize;
            info.sessionStorage.available = true;
        } catch (error) {
            console.warn('Cannot access sessionStorage:', error);
        }

        return info;
    }
}

// Export for use in other modules
window.StorageManager = StorageManager;
