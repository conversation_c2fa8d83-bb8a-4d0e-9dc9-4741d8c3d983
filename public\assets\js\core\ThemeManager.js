/**
 * ThemeManager - Theme management for WIDDX AI
 * Handles dark/light theme switching and user preferences
 */
class ThemeManager extends EventEmitter {
    constructor() {
        super();
        this.currentTheme = 'dark';
        this.systemTheme = 'dark';
        this.themes = {
            light: {
                name: 'Light',
                icon: 'bi-sun-fill',
                colors: {
                    primary: '#0ea5e9',
                    secondary: '#d946ef',
                    background: '#ffffff',
                    surface: '#f8f9fa',
                    text: '#212529',
                    textSecondary: '#495057',
                    border: '#e9ecef'
                }
            },
            dark: {
                name: 'Dark',
                icon: 'bi-moon-fill',
                colors: {
                    primary: '#0ea5e9',
                    secondary: '#d946ef',
                    background: '#0f172a',
                    surface: '#1e293b',
                    text: '#f8fafc',
                    textSecondary: '#cbd5e1',
                    border: '#334155'
                }
            },
            auto: {
                name: 'Auto',
                icon: 'bi-circle-half',
                colors: null // Uses system preference
            }
        };
        
        this.initialize();
    }

    /**
     * Initialize theme manager
     */
    initialize() {
        this.detectSystemTheme();
        this.loadSavedTheme();
        this.setupSystemThemeListener();
        this.applyTheme();
        
        this.emit('theme:initialized', this.currentTheme);
    }

    /**
     * Detect system theme preference
     */
    detectSystemTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            this.systemTheme = 'dark';
        } else {
            this.systemTheme = 'light';
        }
    }

    /**
     * Load saved theme from storage
     */
    loadSavedTheme() {
        try {
            const savedTheme = localStorage.getItem('widdx_ai_theme');
            if (savedTheme && this.themes[savedTheme]) {
                this.currentTheme = savedTheme;
            } else {
                // Default to system preference
                this.currentTheme = 'auto';
            }
        } catch (error) {
            console.warn('Failed to load saved theme:', error);
            this.currentTheme = 'dark';
        }
    }

    /**
     * Set up system theme change listener
     */
    setupSystemThemeListener() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                this.systemTheme = e.matches ? 'dark' : 'light';
                
                // If using auto theme, apply the new system theme
                if (this.currentTheme === 'auto') {
                    this.applyTheme();
                }
                
                this.emit('theme:system-changed', this.systemTheme);
            });
        }
    }

    /**
     * Set theme
     * @param {string} theme - Theme name (light, dark, auto)
     */
    setTheme(theme) {
        if (!this.themes[theme]) {
            console.warn(`Unknown theme: ${theme}`);
            return;
        }

        const previousTheme = this.currentTheme;
        this.currentTheme = theme;
        
        // Save to storage
        try {
            localStorage.setItem('widdx_ai_theme', theme);
        } catch (error) {
            console.warn('Failed to save theme preference:', error);
        }

        this.applyTheme();
        this.emit('theme:changed', { current: theme, previous: previousTheme });
    }

    /**
     * Toggle between light and dark themes
     */
    toggleTheme() {
        if (this.currentTheme === 'light') {
            this.setTheme('dark');
        } else if (this.currentTheme === 'dark') {
            this.setTheme('light');
        } else {
            // If auto, toggle to opposite of current system theme
            this.setTheme(this.systemTheme === 'dark' ? 'light' : 'dark');
        }
    }

    /**
     * Apply current theme to the document
     */
    applyTheme() {
        const effectiveTheme = this.getEffectiveTheme();
        const themeConfig = this.themes[effectiveTheme];
        
        if (!themeConfig) {
            console.error(`Theme configuration not found: ${effectiveTheme}`);
            return;
        }

        // Update document attributes
        document.documentElement.setAttribute('data-bs-theme', effectiveTheme);
        document.documentElement.setAttribute('data-theme', effectiveTheme);
        
        // Update CSS custom properties if theme has colors
        if (themeConfig.colors) {
            this.updateCSSVariables(themeConfig.colors);
        }

        // Update theme toggle button
        this.updateThemeToggleButton();
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(effectiveTheme);
        
        this.emit('theme:applied', effectiveTheme);
    }

    /**
     * Get effective theme (resolves 'auto' to actual theme)
     * @returns {string} - Effective theme name
     */
    getEffectiveTheme() {
        if (this.currentTheme === 'auto') {
            return this.systemTheme;
        }
        return this.currentTheme;
    }

    /**
     * Update CSS custom properties
     * @param {Object} colors - Color configuration
     */
    updateCSSVariables(colors) {
        const root = document.documentElement;
        
        Object.entries(colors).forEach(([key, value]) => {
            root.style.setProperty(`--theme-${key}`, value);
        });
    }

    /**
     * Update theme toggle button
     */
    updateThemeToggleButton() {
        const themeToggle = document.getElementById('theme-toggle');
        if (!themeToggle) return;

        const effectiveTheme = this.getEffectiveTheme();
        const themeConfig = this.themes[this.currentTheme];
        
        // Update button icon
        const icon = themeToggle.querySelector('i');
        if (icon) {
            // Remove all theme icon classes
            icon.className = icon.className.replace(/bi-[a-z-]+/g, '');
            icon.classList.add('bi', themeConfig.icon);
        }

        // Update button title
        themeToggle.title = `Switch to ${effectiveTheme === 'dark' ? 'light' : 'dark'} theme`;
        
        // Update button state
        themeToggle.setAttribute('data-current-theme', this.currentTheme);
        themeToggle.setAttribute('data-effective-theme', effectiveTheme);
    }

    /**
     * Update meta theme-color for mobile browsers
     * @param {string} theme - Theme name
     */
    updateMetaThemeColor(theme) {
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }

        const themeConfig = this.themes[theme];
        if (themeConfig && themeConfig.colors) {
            metaThemeColor.content = themeConfig.colors.background;
        }
    }

    /**
     * Get current theme
     * @returns {string} - Current theme name
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * Get effective theme
     * @returns {string} - Effective theme name
     */
    getEffectiveThemeName() {
        return this.getEffectiveTheme();
    }

    /**
     * Get available themes
     * @returns {Object} - Available themes
     */
    getAvailableThemes() {
        return Object.keys(this.themes).map(key => ({
            key,
            name: this.themes[key].name,
            icon: this.themes[key].icon,
            isCurrent: key === this.currentTheme,
            isEffective: key === this.getEffectiveTheme()
        }));
    }

    /**
     * Get theme configuration
     * @param {string} theme - Theme name
     * @returns {Object|null} - Theme configuration
     */
    getThemeConfig(theme) {
        return this.themes[theme] || null;
    }

    /**
     * Check if dark theme is active
     * @returns {boolean} - True if dark theme is active
     */
    isDarkTheme() {
        return this.getEffectiveTheme() === 'dark';
    }

    /**
     * Check if light theme is active
     * @returns {boolean} - True if light theme is active
     */
    isLightTheme() {
        return this.getEffectiveTheme() === 'light';
    }

    /**
     * Check if auto theme is enabled
     * @returns {boolean} - True if auto theme is enabled
     */
    isAutoTheme() {
        return this.currentTheme === 'auto';
    }

    /**
     * Get system theme preference
     * @returns {string} - System theme preference
     */
    getSystemTheme() {
        return this.systemTheme;
    }

    /**
     * Add custom theme
     * @param {string} name - Theme name
     * @param {Object} config - Theme configuration
     */
    addCustomTheme(name, config) {
        if (this.themes[name]) {
            console.warn(`Theme ${name} already exists, overwriting`);
        }

        this.themes[name] = {
            name: config.displayName || name,
            icon: config.icon || 'bi-palette',
            colors: config.colors,
            custom: true
        };

        this.emit('theme:custom-added', name);
    }

    /**
     * Remove custom theme
     * @param {string} name - Theme name
     */
    removeCustomTheme(name) {
        if (!this.themes[name] || !this.themes[name].custom) {
            console.warn(`Cannot remove built-in theme: ${name}`);
            return;
        }

        // If current theme is being removed, switch to default
        if (this.currentTheme === name) {
            this.setTheme('auto');
        }

        delete this.themes[name];
        this.emit('theme:custom-removed', name);
    }

    /**
     * Export theme configuration
     * @returns {Object} - Theme configuration
     */
    exportThemeConfig() {
        return {
            currentTheme: this.currentTheme,
            systemTheme: this.systemTheme,
            customThemes: Object.entries(this.themes)
                .filter(([key, config]) => config.custom)
                .reduce((acc, [key, config]) => {
                    acc[key] = config;
                    return acc;
                }, {}),
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Import theme configuration
     * @param {Object} config - Theme configuration
     */
    importThemeConfig(config) {
        try {
            // Import custom themes
            if (config.customThemes) {
                Object.entries(config.customThemes).forEach(([name, themeConfig]) => {
                    this.addCustomTheme(name, themeConfig);
                });
            }

            // Set theme if valid
            if (config.currentTheme && this.themes[config.currentTheme]) {
                this.setTheme(config.currentTheme);
            }

            this.emit('theme:config-imported');

        } catch (error) {
            console.error('Failed to import theme configuration:', error);
            this.emit('theme:import-error', error);
        }
    }

    /**
     * Reset to default theme
     */
    resetToDefault() {
        // Remove custom themes
        Object.keys(this.themes).forEach(name => {
            if (this.themes[name].custom) {
                delete this.themes[name];
            }
        });

        // Set to auto theme
        this.setTheme('auto');
        
        this.emit('theme:reset');
    }

    /**
     * Get theme statistics
     * @returns {Object} - Theme usage statistics
     */
    getThemeStats() {
        return {
            currentTheme: this.currentTheme,
            effectiveTheme: this.getEffectiveTheme(),
            systemTheme: this.systemTheme,
            availableThemes: Object.keys(this.themes).length,
            customThemes: Object.values(this.themes).filter(t => t.custom).length,
            isDarkMode: this.isDarkTheme(),
            isAutoMode: this.isAutoTheme(),
            supportsSystemTheme: !!window.matchMedia
        };
    }
}

// Export for use in other modules
window.ThemeManager = ThemeManager;
