/**
 * AuthService - Authentication and user management for WIDDX AI
 * Handles login, logout, token management, and user session
 */
class AuthService extends EventEmitter {
    constructor(apiClient, storageManager) {
        super();
        this.apiClient = apiClient;
        this.storage = storageManager;
        this.user = null;
        this.token = null;
        this.refreshTimer = null;
        this.tokenRefreshThreshold = 5 * 60 * 1000; // 5 minutes before expiry
        
        this.initializeAuth();
    }

    /**
     * Initialize authentication system
     */
    async initializeAuth() {
        try {
            // Try to restore session from storage
            await this.restoreSession();
            
            // Set up token refresh if user is authenticated
            if (this.isAuthenticated()) {
                this.setupTokenRefresh();
            }
            
            this.emit('auth:initialized', this.isAuthenticated());
        } catch (error) {
            console.error('Auth initialization failed:', error);
            this.emit('auth:error', error);
        }
    }

    /**
     * Restore user session from storage
     */
    async restoreSession() {
        const token = this.storage.getLocal('auth_token');
        const user = this.storage.getLocal('user_data');
        
        if (token && user) {
            this.token = token;
            this.user = user;
            this.apiClient.setAuthToken(token);
            
            // Verify token is still valid
            try {
                await this.verifyToken();
                this.emit('auth:session-restored', this.user);
            } catch (error) {
                console.warn('Stored token is invalid, clearing session');
                this.clearSession();
            }
        }
    }

    /**
     * Verify current token with server
     */
    async verifyToken() {
        if (!this.token) {
            throw new Error('No token to verify');
        }

        try {
            const response = await this.apiClient.get('/v2/auth/verify');
            return response.data;
        } catch (error) {
            if (error.status === 401) {
                throw new Error('Token expired or invalid');
            }
            throw error;
        }
    }

    /**
     * Login with email and password
     * @param {string} email - User email
     * @param {string} password - User password
     * @param {boolean} remember - Remember login
     * @returns {Promise<Object>} - User data
     */
    async login(email, password, remember = false) {
        try {
            this.emit('auth:login-start');

            const response = await this.apiClient.post('/auth/login', {
                email,
                password,
                remember
            });

            const { user, token, expires_at } = response.data;

            // Store authentication data
            this.user = user;
            this.token = token;
            this.tokenExpiresAt = new Date(expires_at);

            // Update API client
            this.apiClient.setAuthToken(token);

            // Store in appropriate storage
            const storageMethod = remember ? 'setLocal' : 'setSession';
            this.storage[storageMethod]('auth_token', token, { encrypt: true });
            this.storage[storageMethod]('user_data', user);
            this.storage[storageMethod]('token_expires_at', expires_at);

            // Set up token refresh
            this.setupTokenRefresh();

            this.emit('auth:login-success', user);
            return user;

        } catch (error) {
            this.emit('auth:login-error', error);
            throw error;
        }
    }

    /**
     * Register new user account
     * @param {Object} userData - User registration data
     * @returns {Promise<Object>} - User data
     */
    async register(userData) {
        try {
            this.emit('auth:register-start');

            const response = await this.apiClient.post('/auth/register', userData);
            const { user, token, expires_at } = response.data;

            // Store authentication data
            this.user = user;
            this.token = token;
            this.tokenExpiresAt = new Date(expires_at);

            // Update API client
            this.apiClient.setAuthToken(token);

            // Store in session storage (user can choose to remember later)
            this.storage.setSession('auth_token', token, { encrypt: true });
            this.storage.setSession('user_data', user);
            this.storage.setSession('token_expires_at', expires_at);

            // Set up token refresh
            this.setupTokenRefresh();

            this.emit('auth:register-success', user);
            return user;

        } catch (error) {
            this.emit('auth:register-error', error);
            throw error;
        }
    }

    /**
     * Logout user and clear session
     */
    async logout() {
        try {
            this.emit('auth:logout-start');

            // Notify server about logout
            if (this.token) {
                try {
                    await this.apiClient.post('/auth/logout');
                } catch (error) {
                    console.warn('Server logout failed:', error);
                }
            }

            // Clear local session
            this.clearSession();

            this.emit('auth:logout-success');

        } catch (error) {
            this.emit('auth:logout-error', error);
            // Still clear local session even if server logout fails
            this.clearSession();
        }
    }

    /**
     * Clear authentication session
     */
    clearSession() {
        // Clear user data
        this.user = null;
        this.token = null;
        this.tokenExpiresAt = null;

        // Clear API client token
        this.apiClient.setAuthToken(null);

        // Clear storage
        this.storage.removeLocal('auth_token');
        this.storage.removeLocal('user_data');
        this.storage.removeLocal('token_expires_at');
        this.storage.removeSession('auth_token');
        this.storage.removeSession('user_data');
        this.storage.removeSession('token_expires_at');

        // Clear refresh timer
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
            this.refreshTimer = null;
        }

        this.emit('auth:session-cleared');
    }

    /**
     * Refresh authentication token
     */
    async refreshToken() {
        try {
            this.emit('auth:refresh-start');

            const response = await this.apiClient.post('/auth/refresh');
            const { token, expires_at } = response.data;

            // Update token data
            this.token = token;
            this.tokenExpiresAt = new Date(expires_at);

            // Update API client
            this.apiClient.setAuthToken(token);

            // Update storage
            const hasLocalToken = this.storage.getLocal('auth_token');
            const storageMethod = hasLocalToken ? 'setLocal' : 'setSession';
            this.storage[storageMethod]('auth_token', token, { encrypt: true });
            this.storage[storageMethod]('token_expires_at', expires_at);

            // Set up next refresh
            this.setupTokenRefresh();

            this.emit('auth:refresh-success', token);
            return token;

        } catch (error) {
            this.emit('auth:refresh-error', error);
            
            // If refresh fails, clear session and redirect to login
            if (error.status === 401) {
                this.clearSession();
                this.emit('auth:session-expired');
            }
            
            throw error;
        }
    }

    /**
     * Set up automatic token refresh
     */
    setupTokenRefresh() {
        if (this.refreshTimer) {
            clearTimeout(this.refreshTimer);
        }

        if (!this.tokenExpiresAt) {
            return;
        }

        const now = Date.now();
        const expiresAt = this.tokenExpiresAt.getTime();
        const refreshAt = expiresAt - this.tokenRefreshThreshold;
        const timeUntilRefresh = refreshAt - now;

        if (timeUntilRefresh > 0) {
            this.refreshTimer = setTimeout(() => {
                this.refreshToken().catch(error => {
                    console.error('Automatic token refresh failed:', error);
                });
            }, timeUntilRefresh);
        } else {
            // Token is already expired or about to expire, refresh immediately
            this.refreshToken().catch(error => {
                console.error('Immediate token refresh failed:', error);
            });
        }
    }

    /**
     * Update user profile
     * @param {Object} profileData - Updated profile data
     * @returns {Promise<Object>} - Updated user data
     */
    async updateProfile(profileData) {
        try {
            this.emit('auth:profile-update-start');

            const response = await this.apiClient.put('/v2/auth/profile', profileData);
            const updatedUser = response.data;

            // Update stored user data
            this.user = { ...this.user, ...updatedUser };
            
            const hasLocalUser = this.storage.getLocal('user_data');
            const storageMethod = hasLocalUser ? 'setLocal' : 'setSession';
            this.storage[storageMethod]('user_data', this.user);

            this.emit('auth:profile-updated', this.user);
            return this.user;

        } catch (error) {
            this.emit('auth:profile-update-error', error);
            throw error;
        }
    }

    /**
     * Change user password
     * @param {string} currentPassword - Current password
     * @param {string} newPassword - New password
     * @returns {Promise<boolean>} - Success status
     */
    async changePassword(currentPassword, newPassword) {
        try {
            this.emit('auth:password-change-start');

            await this.apiClient.post('/v2/auth/change-password', {
                current_password: currentPassword,
                new_password: newPassword
            });

            this.emit('auth:password-changed');
            return true;

        } catch (error) {
            this.emit('auth:password-change-error', error);
            throw error;
        }
    }

    /**
     * Request password reset
     * @param {string} email - User email
     * @returns {Promise<boolean>} - Success status
     */
    async requestPasswordReset(email) {
        try {
            await this.apiClient.post('/auth/forgot-password', { email });
            return true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Reset password with token
     * @param {string} token - Reset token
     * @param {string} password - New password
     * @returns {Promise<boolean>} - Success status
     */
    async resetPassword(token, password) {
        try {
            await this.apiClient.post('/auth/reset-password', {
                token,
                password
            });
            return true;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Check if user is authenticated
     * @returns {boolean} - Authentication status
     */
    isAuthenticated() {
        return !!(this.user && this.token);
    }

    /**
     * Check if user has specific permission
     * @param {string} permission - Permission to check
     * @returns {boolean} - Permission status
     */
    hasPermission(permission) {
        if (!this.user || !this.user.permissions) {
            return false;
        }
        return this.user.permissions.includes(permission);
    }

    /**
     * Check if user has specific role
     * @param {string} role - Role to check
     * @returns {boolean} - Role status
     */
    hasRole(role) {
        if (!this.user || !this.user.roles) {
            return false;
        }
        return this.user.roles.includes(role);
    }

    /**
     * Get current user data
     * @returns {Object|null} - User data or null
     */
    getUser() {
        return this.user;
    }

    /**
     * Get current authentication token
     * @returns {string|null} - Token or null
     */
    getToken() {
        return this.token;
    }

    /**
     * Check if token is about to expire
     * @returns {boolean} - True if token expires soon
     */
    isTokenExpiringSoon() {
        if (!this.tokenExpiresAt) {
            return false;
        }
        
        const now = Date.now();
        const expiresAt = this.tokenExpiresAt.getTime();
        return (expiresAt - now) < this.tokenRefreshThreshold;
    }

    /**
     * Get time until token expires
     * @returns {number} - Milliseconds until expiry
     */
    getTimeUntilExpiry() {
        if (!this.tokenExpiresAt) {
            return 0;
        }
        
        return Math.max(0, this.tokenExpiresAt.getTime() - Date.now());
    }
}

// Export for use in other modules
window.AuthService = AuthService;
