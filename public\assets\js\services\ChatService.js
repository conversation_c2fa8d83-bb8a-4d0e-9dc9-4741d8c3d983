/**
 * ChatService - Chat and conversation management for WIDDX AI
 * Handles conversations, messages, and real-time communication
 */
class ChatService extends EventEmitter {
    constructor(apiClient, storageManager) {
        super();
        this.apiClient = apiClient;
        this.storage = storageManager;
        this.conversations = new Map();
        this.currentConversation = null;
        this.messageCache = new Map();
        this.typingUsers = new Set();
        this.maxCachedMessages = 1000;
        
        this.initializeChat();
    }

    /**
     * Initialize chat service
     */
    async initializeChat() {
        try {
            // Load cached conversations
            await this.loadCachedConversations();
            
            // Load recent conversations from server
            await this.loadRecentConversations();
            
            this.emit('chat:initialized');
        } catch (error) {
            console.error('Chat initialization failed:', error);
            this.emit('chat:error', error);
        }
    }

    /**
     * Load cached conversations from storage
     */
    async loadCachedConversations() {
        try {
            if (this.storage.db) {
                // Load from IndexedDB
                const transaction = this.storage.db.transaction(['conversations'], 'readonly');
                const store = transaction.objectStore('conversations');
                const request = store.getAll();
                
                request.onsuccess = () => {
                    const conversations = request.result;
                    conversations.forEach(conv => {
                        this.conversations.set(conv.id, conv);
                    });
                    this.emit('chat:conversations-loaded', conversations);
                };
            } else {
                // Fallback to localStorage
                const cached = this.storage.getLocal('conversations', []);
                cached.forEach(conv => {
                    this.conversations.set(conv.id, conv);
                });
            }
        } catch (error) {
            console.warn('Failed to load cached conversations:', error);
        }
    }

    /**
     * Load recent conversations from server
     * @param {number} limit - Number of conversations to load
     * @returns {Promise<Array>} - Array of conversations
     */
    async loadRecentConversations(limit = 20) {
        try {
            const response = await this.apiClient.get('/v2/conversations', {
                params: { limit, sort: 'last_activity_at', order: 'desc' }
            });

            const conversations = response.data.data || response.data;
            
            // Update local cache
            conversations.forEach(conv => {
                this.conversations.set(conv.id, conv);
                this.cacheConversation(conv);
            });

            this.emit('chat:conversations-updated', Array.from(this.conversations.values()));
            return conversations;

        } catch (error) {
            console.error('Failed to load conversations:', error);
            this.emit('chat:error', error);
            throw error;
        }
    }

    /**
     * Create new conversation
     * @param {Object} options - Conversation options
     * @returns {Promise<Object>} - Created conversation
     */
    async createConversation(options = {}) {
        try {
            this.emit('chat:conversation-creating');

            const response = await this.apiClient.post('/v2/conversations', {
                title: options.title || 'New Conversation',
                personality_id: options.personalityId,
                context: options.context || {},
                initial_message: options.initialMessage
            });

            const conversation = response.data;
            
            // Add to local cache
            this.conversations.set(conversation.id, conversation);
            this.cacheConversation(conversation);

            // Set as current conversation
            this.currentConversation = conversation;

            this.emit('chat:conversation-created', conversation);
            return conversation;

        } catch (error) {
            this.emit('chat:conversation-create-error', error);
            throw error;
        }
    }

    /**
     * Get conversation by ID
     * @param {number} conversationId - Conversation ID
     * @param {boolean} forceRefresh - Force refresh from server
     * @returns {Promise<Object>} - Conversation data
     */
    async getConversation(conversationId, forceRefresh = false) {
        try {
            // Check cache first
            if (!forceRefresh && this.conversations.has(conversationId)) {
                const cached = this.conversations.get(conversationId);
                this.emit('chat:conversation-loaded', cached);
                return cached;
            }

            // Load from server
            const response = await this.apiClient.get(`/v2/conversations/${conversationId}`);
            const conversation = response.data;

            // Update cache
            this.conversations.set(conversationId, conversation);
            this.cacheConversation(conversation);

            this.emit('chat:conversation-loaded', conversation);
            return conversation;

        } catch (error) {
            this.emit('chat:conversation-load-error', error);
            throw error;
        }
    }

    /**
     * Set current active conversation
     * @param {number} conversationId - Conversation ID
     * @returns {Promise<Object>} - Conversation data
     */
    async setCurrentConversation(conversationId) {
        try {
            const conversation = await this.getConversation(conversationId);
            this.currentConversation = conversation;
            
            // Load messages for this conversation
            await this.loadMessages(conversationId);
            
            this.emit('chat:conversation-changed', conversation);
            return conversation;

        } catch (error) {
            this.emit('chat:conversation-change-error', error);
            throw error;
        }
    }

    /**
     * Load messages for a conversation
     * @param {number} conversationId - Conversation ID
     * @param {Object} options - Loading options
     * @returns {Promise<Array>} - Array of messages
     */
    async loadMessages(conversationId, options = {}) {
        try {
            const params = {
                limit: options.limit || 50,
                offset: options.offset || 0,
                sort: options.sort || 'created_at',
                order: options.order || 'asc'
            };

            const response = await this.apiClient.get(`/v2/conversations/${conversationId}/messages`, { params });
            const messages = response.data.data || response.data;

            // Cache messages
            const cacheKey = `messages_${conversationId}`;
            let cachedMessages = this.messageCache.get(cacheKey) || [];
            
            // Merge with existing messages (avoid duplicates)
            const messageMap = new Map();
            [...cachedMessages, ...messages].forEach(msg => {
                messageMap.set(msg.id, msg);
            });
            
            const allMessages = Array.from(messageMap.values()).sort((a, b) => 
                new Date(a.created_at) - new Date(b.created_at)
            );

            this.messageCache.set(cacheKey, allMessages);
            this.cacheMessages(conversationId, allMessages);

            this.emit('chat:messages-loaded', { conversationId, messages: allMessages });
            return allMessages;

        } catch (error) {
            this.emit('chat:messages-load-error', error);
            throw error;
        }
    }

    /**
     * Send message to conversation
     * @param {number} conversationId - Conversation ID
     * @param {string} content - Message content
     * @param {Object} options - Message options
     * @returns {Promise<Object>} - Sent message
     */
    async sendMessage(conversationId, content, options = {}) {
        try {
            this.emit('chat:message-sending', { conversationId, content });

            // Create optimistic message for immediate UI update
            const optimisticMessage = {
                id: `temp_${Date.now()}`,
                conversation_id: conversationId,
                role: 'user',
                content,
                created_at: new Date().toISOString(),
                status: 'sending',
                attachments: options.attachments || []
            };

            // Add to cache immediately
            this.addMessageToCache(conversationId, optimisticMessage);
            this.emit('chat:message-added', optimisticMessage);

            // Prepare request data
            const requestData = {
                message: content,
                use_reasoning: options.useReasoning !== false,
                preferred_model: options.preferredModel,
                attachments: options.attachments,
                context: options.context
            };

            // Send to server
            const response = await this.apiClient.post(
                `/v2/conversations/${conversationId}/messages`,
                requestData
            );

            const sentMessage = response.data;

            // Replace optimistic message with real message
            this.replaceMessageInCache(conversationId, optimisticMessage.id, sentMessage);
            this.emit('chat:message-sent', sentMessage);

            // Update conversation last activity
            if (this.conversations.has(conversationId)) {
                const conversation = this.conversations.get(conversationId);
                conversation.last_activity_at = sentMessage.created_at;
                this.conversations.set(conversationId, conversation);
            }

            return sentMessage;

        } catch (error) {
            // Mark optimistic message as failed
            const cacheKey = `messages_${conversationId}`;
            const messages = this.messageCache.get(cacheKey) || [];
            const failedMessage = messages.find(m => m.content === content && m.status === 'sending');
            
            if (failedMessage) {
                failedMessage.status = 'failed';
                failedMessage.error = error.message;
                this.emit('chat:message-failed', failedMessage);
            }

            this.emit('chat:message-send-error', error);
            throw error;
        }
    }

    /**
     * Regenerate last AI response
     * @param {number} conversationId - Conversation ID
     * @returns {Promise<Object>} - New response message
     */
    async regenerateResponse(conversationId) {
        try {
            this.emit('chat:regenerating-response', conversationId);

            const response = await this.apiClient.post(`/v2/conversations/${conversationId}/regenerate`);
            const newMessage = response.data;

            // Add to cache
            this.addMessageToCache(conversationId, newMessage);
            this.emit('chat:response-regenerated', newMessage);

            return newMessage;

        } catch (error) {
            this.emit('chat:regenerate-error', error);
            throw error;
        }
    }

    /**
     * Delete a message
     * @param {number} conversationId - Conversation ID
     * @param {number} messageId - Message ID
     * @returns {Promise<boolean>} - Success status
     */
    async deleteMessage(conversationId, messageId) {
        try {
            await this.apiClient.delete(`/v2/conversations/${conversationId}/messages/${messageId}`);
            
            // Remove from cache
            this.removeMessageFromCache(conversationId, messageId);
            this.emit('chat:message-deleted', { conversationId, messageId });

            return true;

        } catch (error) {
            this.emit('chat:message-delete-error', error);
            throw error;
        }
    }

    /**
     * Update conversation title
     * @param {number} conversationId - Conversation ID
     * @param {string} title - New title
     * @returns {Promise<Object>} - Updated conversation
     */
    async updateConversationTitle(conversationId, title) {
        try {
            const response = await this.apiClient.put(`/v2/conversations/${conversationId}`, { title });
            const updatedConversation = response.data;

            // Update cache
            this.conversations.set(conversationId, updatedConversation);
            this.cacheConversation(updatedConversation);

            this.emit('chat:conversation-updated', updatedConversation);
            return updatedConversation;

        } catch (error) {
            this.emit('chat:conversation-update-error', error);
            throw error;
        }
    }

    /**
     * Delete conversation
     * @param {number} conversationId - Conversation ID
     * @returns {Promise<boolean>} - Success status
     */
    async deleteConversation(conversationId) {
        try {
            await this.apiClient.delete(`/v2/conversations/${conversationId}`);
            
            // Remove from cache
            this.conversations.delete(conversationId);
            this.messageCache.delete(`messages_${conversationId}`);
            
            // Remove from storage
            if (this.storage.db) {
                const transaction = this.storage.db.transaction(['conversations', 'messages'], 'readwrite');
                transaction.objectStore('conversations').delete(conversationId);
                
                // Remove associated messages
                const messageStore = transaction.objectStore('messages');
                const index = messageStore.index('conversationId');
                const range = IDBKeyRange.only(conversationId);
                index.openCursor(range).onsuccess = (event) => {
                    const cursor = event.target.result;
                    if (cursor) {
                        cursor.delete();
                        cursor.continue();
                    }
                };
            }

            this.emit('chat:conversation-deleted', conversationId);
            return true;

        } catch (error) {
            this.emit('chat:conversation-delete-error', error);
            throw error;
        }
    }

    /**
     * Export conversation
     * @param {number} conversationId - Conversation ID
     * @param {string} format - Export format (json, txt, pdf)
     * @returns {Promise<Blob>} - Exported data
     */
    async exportConversation(conversationId, format = 'json') {
        try {
            const response = await this.apiClient.get(`/v2/conversations/${conversationId}/export`, {
                params: { format },
                responseType: 'blob'
            });

            this.emit('chat:conversation-exported', { conversationId, format });
            return response.data;

        } catch (error) {
            this.emit('chat:export-error', error);
            throw error;
        }
    }

    /**
     * Search conversations
     * @param {string} query - Search query
     * @param {Object} filters - Search filters
     * @returns {Promise<Array>} - Search results
     */
    async searchConversations(query, filters = {}) {
        try {
            const params = {
                q: query,
                ...filters
            };

            const response = await this.apiClient.get('/v2/conversations/search', { params });
            const results = response.data.data || response.data;

            this.emit('chat:search-completed', { query, results });
            return results;

        } catch (error) {
            this.emit('chat:search-error', error);
            throw error;
        }
    }

    // Cache management methods
    addMessageToCache(conversationId, message) {
        const cacheKey = `messages_${conversationId}`;
        const messages = this.messageCache.get(cacheKey) || [];
        messages.push(message);
        
        // Sort by creation time
        messages.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));
        
        this.messageCache.set(cacheKey, messages);
    }

    replaceMessageInCache(conversationId, tempId, realMessage) {
        const cacheKey = `messages_${conversationId}`;
        const messages = this.messageCache.get(cacheKey) || [];
        const index = messages.findIndex(m => m.id === tempId);
        
        if (index !== -1) {
            messages[index] = realMessage;
            this.messageCache.set(cacheKey, messages);
        }
    }

    removeMessageFromCache(conversationId, messageId) {
        const cacheKey = `messages_${conversationId}`;
        const messages = this.messageCache.get(cacheKey) || [];
        const filtered = messages.filter(m => m.id !== messageId);
        this.messageCache.set(cacheKey, filtered);
    }

    async cacheConversation(conversation) {
        if (this.storage.db) {
            try {
                await this.storage.setIndexedDB('conversations', conversation);
            } catch (error) {
                console.warn('Failed to cache conversation:', error);
            }
        }
    }

    async cacheMessages(conversationId, messages) {
        if (this.storage.db) {
            try {
                const transaction = this.storage.db.transaction(['messages'], 'readwrite');
                const store = transaction.objectStore('messages');
                
                messages.forEach(message => {
                    store.put({ ...message, conversationId });
                });
            } catch (error) {
                console.warn('Failed to cache messages:', error);
            }
        }
    }

    // Getters
    getCurrentConversation() {
        return this.currentConversation;
    }

    getConversations() {
        return Array.from(this.conversations.values());
    }

    getMessages(conversationId) {
        return this.messageCache.get(`messages_${conversationId}`) || [];
    }

    isTyping(conversationId) {
        return this.typingUsers.has(conversationId);
    }
}

// Export for use in other modules
window.ChatService = ChatService;
