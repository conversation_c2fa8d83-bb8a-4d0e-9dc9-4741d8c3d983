/**
 * FileService - File upload and management for WIDDX AI
 * Handles file uploads, processing, and multi-modal content
 */
class FileService extends EventEmitter {
    constructor(apiClient, storageManager) {
        super();
        this.apiClient = apiClient;
        this.storage = storageManager;
        this.uploadQueue = new Map();
        this.maxFileSize = 10 * 1024 * 1024; // 10MB
        this.supportedTypes = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain',
            'text/markdown',
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
            'audio/mpeg',
            'audio/wav',
            'audio/ogg',
            'audio/m4a',
            'text/csv',
            'application/json',
            'application/xml'
        ];
    }

    /**
     * Upload file with progress tracking
     * @param {FormData} formData - Form data containing file
     * @param {Object} options - Upload options
     * @returns {Promise<Object>} - Upload result
     */
    async uploadFile(formData, options = {}) {
        const file = formData.get('file');
        if (!file) {
            throw new Error('No file provided');
        }

        // Validate file
        this.validateFile(file);

        const uploadId = this.generateUploadId();
        
        try {
            this.emit('file:upload-start', { uploadId, file });

            // Add to upload queue
            this.uploadQueue.set(uploadId, {
                file,
                status: 'uploading',
                progress: 0,
                startTime: Date.now()
            });

            // Upload with progress tracking
            const result = await this.apiClient.upload(
                '/v2/files/upload',
                formData,
                (progress, loaded, total) => {
                    this.updateUploadProgress(uploadId, progress, loaded, total);
                }
            );

            // Update upload status
            this.uploadQueue.set(uploadId, {
                ...this.uploadQueue.get(uploadId),
                status: 'completed',
                progress: 100,
                result: result.data
            });

            this.emit('file:upload-complete', { uploadId, result: result.data });
            return result.data;

        } catch (error) {
            // Update upload status
            this.uploadQueue.set(uploadId, {
                ...this.uploadQueue.get(uploadId),
                status: 'failed',
                error: error.message
            });

            this.emit('file:upload-error', { uploadId, error });
            throw error;
        }
    }

    /**
     * Upload multiple files
     * @param {FileList|Array} files - Files to upload
     * @param {Object} options - Upload options
     * @returns {Promise<Array>} - Array of upload results
     */
    async uploadMultipleFiles(files, options = {}) {
        const uploadPromises = Array.from(files).map(async (file) => {
            const formData = new FormData();
            formData.append('file', file);
            
            if (options.description) {
                formData.append('description', options.description);
            }
            
            return this.uploadFile(formData, options);
        });

        try {
            const results = await Promise.allSettled(uploadPromises);
            
            const successful = results
                .filter(result => result.status === 'fulfilled')
                .map(result => result.value);
                
            const failed = results
                .filter(result => result.status === 'rejected')
                .map(result => result.reason);

            this.emit('file:batch-upload-complete', { successful, failed });
            
            return { successful, failed };

        } catch (error) {
            this.emit('file:batch-upload-error', error);
            throw error;
        }
    }

    /**
     * Get uploaded files list
     * @param {Object} filters - File filters
     * @returns {Promise<Array>} - Array of files
     */
    async getFiles(filters = {}) {
        try {
            const params = {
                type: filters.type,
                limit: filters.limit || 20,
                offset: filters.offset || 0,
                sort: filters.sort || 'created_at',
                order: filters.order || 'desc'
            };

            const response = await this.apiClient.get('/v2/files', { params });
            const files = response.data.data || response.data;

            this.emit('file:files-loaded', files);
            return files;

        } catch (error) {
            this.emit('file:files-load-error', error);
            throw error;
        }
    }

    /**
     * Get file details
     * @param {number} fileId - File ID
     * @returns {Promise<Object>} - File details
     */
    async getFileDetails(fileId) {
        try {
            const response = await this.apiClient.get(`/v2/files/${fileId}`);
            const file = response.data;

            this.emit('file:details-loaded', file);
            return file;

        } catch (error) {
            this.emit('file:details-load-error', error);
            throw error;
        }
    }

    /**
     * Process uploaded file
     * @param {number} fileId - File ID
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} - Processing result
     */
    async processFile(fileId, options = {}) {
        try {
            this.emit('file:processing-start', fileId);

            const response = await this.apiClient.post(`/v2/files/${fileId}/process`, {
                extract_text: options.extractText !== false,
                analyze_content: options.analyzeContent !== false,
                generate_summary: options.generateSummary !== false
            });

            const result = response.data;
            this.emit('file:processing-complete', { fileId, result });
            return result;

        } catch (error) {
            this.emit('file:processing-error', { fileId, error });
            throw error;
        }
    }

    /**
     * Get file processing results
     * @param {number} fileId - File ID
     * @returns {Promise<Object>} - Processing results
     */
    async getProcessingResults(fileId) {
        try {
            const response = await this.apiClient.get(`/v2/files/${fileId}/analysis`);
            return response.data;

        } catch (error) {
            this.emit('file:analysis-load-error', error);
            throw error;
        }
    }

    /**
     * Extract text from file
     * @param {number} fileId - File ID
     * @returns {Promise<Object>} - Extracted text
     */
    async extractText(fileId) {
        try {
            const response = await this.apiClient.post(`/v2/files/${fileId}/extract-text`);
            return response.data;

        } catch (error) {
            this.emit('file:text-extraction-error', error);
            throw error;
        }
    }

    /**
     * Delete file
     * @param {number} fileId - File ID
     * @returns {Promise<boolean>} - Success status
     */
    async deleteFile(fileId) {
        try {
            await this.apiClient.delete(`/v2/files/${fileId}`);
            this.emit('file:deleted', fileId);
            return true;

        } catch (error) {
            this.emit('file:delete-error', error);
            throw error;
        }
    }

    /**
     * Validate file before upload
     * @param {File} file - File to validate
     * @throws {Error} - Validation error
     */
    validateFile(file) {
        // Check file size
        if (file.size > this.maxFileSize) {
            throw new Error(`File size exceeds maximum limit of ${this.formatFileSize(this.maxFileSize)}`);
        }

        // Check file type
        if (!this.supportedTypes.includes(file.type)) {
            throw new Error(`File type "${file.type}" is not supported`);
        }

        // Check file name
        if (!file.name || file.name.trim() === '') {
            throw new Error('File must have a valid name');
        }

        // Check for potentially dangerous file extensions
        const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
        
        if (dangerousExtensions.includes(fileExtension)) {
            throw new Error('File type not allowed for security reasons');
        }
    }

    /**
     * Update upload progress
     * @param {string} uploadId - Upload ID
     * @param {number} progress - Progress percentage
     * @param {number} loaded - Bytes loaded
     * @param {number} total - Total bytes
     */
    updateUploadProgress(uploadId, progress, loaded, total) {
        const upload = this.uploadQueue.get(uploadId);
        if (upload) {
            upload.progress = progress;
            upload.loaded = loaded;
            upload.total = total;
            
            this.emit('file:upload-progress', {
                uploadId,
                progress,
                loaded,
                total,
                file: upload.file
            });
        }
    }

    /**
     * Get upload status
     * @param {string} uploadId - Upload ID
     * @returns {Object|null} - Upload status or null
     */
    getUploadStatus(uploadId) {
        return this.uploadQueue.get(uploadId) || null;
    }

    /**
     * Get all active uploads
     * @returns {Array} - Array of active uploads
     */
    getActiveUploads() {
        return Array.from(this.uploadQueue.entries())
            .filter(([id, upload]) => upload.status === 'uploading')
            .map(([id, upload]) => ({ id, ...upload }));
    }

    /**
     * Cancel upload
     * @param {string} uploadId - Upload ID
     * @returns {boolean} - Success status
     */
    cancelUpload(uploadId) {
        const upload = this.uploadQueue.get(uploadId);
        if (upload && upload.status === 'uploading') {
            upload.status = 'cancelled';
            this.emit('file:upload-cancelled', uploadId);
            return true;
        }
        return false;
    }

    /**
     * Clear completed uploads from queue
     */
    clearCompletedUploads() {
        for (const [uploadId, upload] of this.uploadQueue) {
            if (upload.status === 'completed' || upload.status === 'failed') {
                this.uploadQueue.delete(uploadId);
            }
        }
    }

    /**
     * Generate unique upload ID
     * @returns {string} - Unique upload ID
     */
    generateUploadId() {
        return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Format file size for display
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get file type icon
     * @param {string} mimeType - File MIME type
     * @returns {string} - Bootstrap icon class
     */
    getFileTypeIcon(mimeType) {
        const iconMap = {
            'application/pdf': 'bi-file-pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'bi-file-word',
            'text/plain': 'bi-file-text',
            'text/markdown': 'bi-file-text',
            'text/csv': 'bi-file-spreadsheet',
            'application/json': 'bi-file-code',
            'application/xml': 'bi-file-code'
        };

        if (mimeType.startsWith('image/')) return 'bi-file-image';
        if (mimeType.startsWith('audio/')) return 'bi-file-music';
        if (mimeType.startsWith('video/')) return 'bi-file-play';

        return iconMap[mimeType] || 'bi-file';
    }

    /**
     * Get supported file types for display
     * @returns {Array} - Array of supported file type info
     */
    getSupportedFileTypes() {
        return [
            { type: 'Documents', extensions: ['PDF', 'DOCX', 'TXT', 'MD'], icon: 'bi-file-text' },
            { type: 'Images', extensions: ['JPG', 'PNG', 'GIF', 'WebP'], icon: 'bi-file-image' },
            { type: 'Audio', extensions: ['MP3', 'WAV', 'OGG', 'M4A'], icon: 'bi-file-music' },
            { type: 'Data', extensions: ['CSV', 'JSON', 'XML'], icon: 'bi-file-code' }
        ];
    }

    /**
     * Create file preview URL
     * @param {Object} file - File object
     * @returns {string|null} - Preview URL or null
     */
    createPreviewUrl(file) {
        if (!file || !file.id) return null;

        // For images, return direct URL
        if (file.mime_type && file.mime_type.startsWith('image/')) {
            return `/api/v2/files/${file.id}/preview`;
        }

        return null;
    }

    /**
     * Download file
     * @param {number} fileId - File ID
     * @param {string} filename - Optional filename
     * @returns {Promise<Blob>} - File blob
     */
    async downloadFile(fileId, filename = null) {
        try {
            const response = await this.apiClient.get(`/v2/files/${fileId}/download`, {
                responseType: 'blob'
            });

            // Create download link
            const blob = response.data;
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename || `file_${fileId}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            this.emit('file:downloaded', fileId);
            return blob;

        } catch (error) {
            this.emit('file:download-error', error);
            throw error;
        }
    }

    /**
     * Get file statistics
     * @returns {Promise<Object>} - File statistics
     */
    async getFileStatistics() {
        try {
            const response = await this.apiClient.get('/v2/files/statistics');
            return response.data;

        } catch (error) {
            this.emit('file:stats-error', error);
            throw error;
        }
    }

    /**
     * Search files
     * @param {string} query - Search query
     * @param {Object} filters - Search filters
     * @returns {Promise<Array>} - Search results
     */
    async searchFiles(query, filters = {}) {
        try {
            const params = {
                q: query,
                type: filters.type,
                date_from: filters.dateFrom,
                date_to: filters.dateTo,
                limit: filters.limit || 20,
                offset: filters.offset || 0
            };

            const response = await this.apiClient.get('/v2/files/search', { params });
            const results = response.data.data || response.data;

            this.emit('file:search-complete', { query, results });
            return results;

        } catch (error) {
            this.emit('file:search-error', error);
            throw error;
        }
    }
}

// Export for use in other modules
window.FileService = FileService;
