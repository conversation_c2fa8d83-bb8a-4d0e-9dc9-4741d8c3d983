/**
 * KnowledgeService - Knowledge base management for WIDDX AI
 * Handles knowledge entries, search, and optimization
 */
class KnowledgeService extends EventEmitter {
    constructor(apiClient, storageManager) {
        super();
        this.apiClient = apiClient;
        this.storage = storageManager;
        this.knowledgeEntries = new Map();
        this.searchCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    /**
     * Search knowledge base
     * @param {string} query - Search query
     * @param {Object} filters - Search filters
     * @returns {Promise<Array>} - Search results
     */
    async searchKnowledge(query, filters = {}) {
        try {
            const cacheKey = this.generateCacheKey(query, filters);
            
            // Check cache first
            if (this.searchCache.has(cacheKey)) {
                const cached = this.searchCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    this.emit('knowledge:search-complete', cached.results);
                    return cached.results;
                }
            }

            this.emit('knowledge:search-start', query);

            const params = {
                q: query,
                limit: filters.limit || 20,
                offset: filters.offset || 0,
                confidence_threshold: filters.confidenceThreshold || 0.5,
                verified_only: filters.verifiedOnly || false,
                tags: filters.tags,
                source_type: filters.sourceType
            };

            const response = await this.apiClient.get('/v2/knowledge/search', { params });
            const results = response.data.data || response.data;

            // Cache results
            this.searchCache.set(cacheKey, {
                results,
                timestamp: Date.now()
            });

            this.emit('knowledge:search-complete', results);
            return results;

        } catch (error) {
            this.emit('knowledge:search-error', error);
            throw error;
        }
    }

    /**
     * Get knowledge entries
     * @param {Object} filters - Entry filters
     * @returns {Promise<Array>} - Array of knowledge entries
     */
    async getKnowledgeEntries(filters = {}) {
        try {
            const params = {
                limit: filters.limit || 50,
                offset: filters.offset || 0,
                sort: filters.sort || 'created_at',
                order: filters.order || 'desc',
                verified_only: filters.verifiedOnly,
                tags: filters.tags,
                source_type: filters.sourceType
            };

            const response = await this.apiClient.get('/v2/knowledge/entries', { params });
            const entries = response.data.data || response.data;

            // Update local cache
            entries.forEach(entry => {
                this.knowledgeEntries.set(entry.id, entry);
            });

            this.emit('knowledge:entries-loaded', entries);
            return entries;

        } catch (error) {
            this.emit('knowledge:entries-load-error', error);
            throw error;
        }
    }

    /**
     * Get knowledge entry by ID
     * @param {number} entryId - Entry ID
     * @returns {Promise<Object>} - Knowledge entry
     */
    async getKnowledgeEntry(entryId) {
        try {
            // Check cache first
            if (this.knowledgeEntries.has(entryId)) {
                const cached = this.knowledgeEntries.get(entryId);
                this.emit('knowledge:entry-loaded', cached);
                return cached;
            }

            const response = await this.apiClient.get(`/v2/knowledge/entries/${entryId}`);
            const entry = response.data;

            // Update cache
            this.knowledgeEntries.set(entryId, entry);

            this.emit('knowledge:entry-loaded', entry);
            return entry;

        } catch (error) {
            this.emit('knowledge:entry-load-error', error);
            throw error;
        }
    }

    /**
     * Create knowledge entry
     * @param {Object} entryData - Entry data
     * @returns {Promise<Object>} - Created entry
     */
    async createKnowledgeEntry(entryData) {
        try {
            this.emit('knowledge:entry-creating');

            const response = await this.apiClient.post('/v2/knowledge/entries', entryData);
            const entry = response.data;

            // Update cache
            this.knowledgeEntries.set(entry.id, entry);

            // Clear search cache
            this.searchCache.clear();

            this.emit('knowledge:entry-created', entry);
            return entry;

        } catch (error) {
            this.emit('knowledge:entry-create-error', error);
            throw error;
        }
    }

    /**
     * Update knowledge entry
     * @param {number} entryId - Entry ID
     * @param {Object} updateData - Updated data
     * @returns {Promise<Object>} - Updated entry
     */
    async updateKnowledgeEntry(entryId, updateData) {
        try {
            const response = await this.apiClient.put(`/v2/knowledge/entries/${entryId}`, updateData);
            const entry = response.data;

            // Update cache
            this.knowledgeEntries.set(entryId, entry);

            // Clear search cache
            this.searchCache.clear();

            this.emit('knowledge:entry-updated', entry);
            return entry;

        } catch (error) {
            this.emit('knowledge:entry-update-error', error);
            throw error;
        }
    }

    /**
     * Delete knowledge entry
     * @param {number} entryId - Entry ID
     * @returns {Promise<boolean>} - Success status
     */
    async deleteKnowledgeEntry(entryId) {
        try {
            await this.apiClient.delete(`/v2/knowledge/entries/${entryId}`);

            // Remove from cache
            this.knowledgeEntries.delete(entryId);

            // Clear search cache
            this.searchCache.clear();

            this.emit('knowledge:entry-deleted', entryId);
            return true;

        } catch (error) {
            this.emit('knowledge:entry-delete-error', error);
            throw error;
        }
    }

    /**
     * Verify knowledge entry
     * @param {number} entryId - Entry ID
     * @returns {Promise<Object>} - Verified entry
     */
    async verifyKnowledgeEntry(entryId) {
        try {
            const response = await this.apiClient.post(`/v2/knowledge/entries/${entryId}/verify`);
            const entry = response.data;

            // Update cache
            this.knowledgeEntries.set(entryId, entry);

            this.emit('knowledge:entry-verified', entry);
            return entry;

        } catch (error) {
            this.emit('knowledge:entry-verify-error', error);
            throw error;
        }
    }

    /**
     * Find similar knowledge entries
     * @param {number} entryId - Entry ID
     * @param {number} limit - Number of similar entries to return
     * @returns {Promise<Array>} - Similar entries
     */
    async findSimilarEntries(entryId, limit = 5) {
        try {
            const params = { limit };
            const response = await this.apiClient.get(`/v2/knowledge/similar/${entryId}`, { params });
            const similarEntries = response.data.data || response.data;

            this.emit('knowledge:similar-entries-found', { entryId, similarEntries });
            return similarEntries;

        } catch (error) {
            this.emit('knowledge:similar-entries-error', error);
            throw error;
        }
    }

    /**
     * Optimize knowledge base
     * @param {Object} options - Optimization options
     * @returns {Promise<Object>} - Optimization results
     */
    async optimizeKnowledgeBase(options = {}) {
        try {
            this.emit('knowledge:optimization-start');

            const response = await this.apiClient.post('/v2/knowledge/optimize', {
                remove_duplicates: options.removeDuplicates !== false,
                update_embeddings: options.updateEmbeddings !== false,
                merge_similar: options.mergeSimilar || false,
                similarity_threshold: options.similarityThreshold || 0.9
            });

            const results = response.data;

            // Clear caches after optimization
            this.knowledgeEntries.clear();
            this.searchCache.clear();

            this.emit('knowledge:optimization-complete', results);
            return results;

        } catch (error) {
            this.emit('knowledge:optimization-error', error);
            throw error;
        }
    }

    /**
     * Get knowledge base statistics
     * @returns {Promise<Object>} - Knowledge base statistics
     */
    async getKnowledgeStats() {
        try {
            const response = await this.apiClient.get('/v2/knowledge/stats');
            const stats = response.data;

            this.emit('knowledge:stats-loaded', stats);
            return stats;

        } catch (error) {
            this.emit('knowledge:stats-error', error);
            throw error;
        }
    }

    /**
     * Import knowledge from file
     * @param {File} file - File to import
     * @param {Object} options - Import options
     * @returns {Promise<Object>} - Import results
     */
    async importKnowledge(file, options = {}) {
        try {
            this.emit('knowledge:import-start');

            const formData = new FormData();
            formData.append('file', file);
            formData.append('source_type', options.sourceType || 'import');
            formData.append('auto_verify', options.autoVerify || false);
            formData.append('extract_metadata', options.extractMetadata !== false);

            const response = await this.apiClient.post('/v2/knowledge/import', formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            });

            const results = response.data;

            // Clear caches after import
            this.knowledgeEntries.clear();
            this.searchCache.clear();

            this.emit('knowledge:import-complete', results);
            return results;

        } catch (error) {
            this.emit('knowledge:import-error', error);
            throw error;
        }
    }

    /**
     * Export knowledge base
     * @param {Object} filters - Export filters
     * @param {string} format - Export format (json, csv, txt)
     * @returns {Promise<Blob>} - Exported data
     */
    async exportKnowledge(filters = {}, format = 'json') {
        try {
            const params = {
                format,
                verified_only: filters.verifiedOnly,
                tags: filters.tags,
                source_type: filters.sourceType,
                date_from: filters.dateFrom,
                date_to: filters.dateTo
            };

            const response = await this.apiClient.get('/v2/knowledge/export', {
                params,
                responseType: 'blob'
            });

            this.emit('knowledge:export-complete', { format, filters });
            return response.data;

        } catch (error) {
            this.emit('knowledge:export-error', error);
            throw error;
        }
    }

    /**
     * Get knowledge tags
     * @returns {Promise<Array>} - Array of tags
     */
    async getKnowledgeTags() {
        try {
            const response = await this.apiClient.get('/v2/knowledge/tags');
            const tags = response.data.data || response.data;

            this.emit('knowledge:tags-loaded', tags);
            return tags;

        } catch (error) {
            this.emit('knowledge:tags-error', error);
            throw error;
        }
    }

    /**
     * Generate cache key for search
     * @param {string} query - Search query
     * @param {Object} filters - Search filters
     * @returns {string} - Cache key
     */
    generateCacheKey(query, filters) {
        const key = JSON.stringify({ query, filters });
        return btoa(key).replace(/[^a-zA-Z0-9]/g, '');
    }

    /**
     * Clear search cache
     */
    clearSearchCache() {
        this.searchCache.clear();
        this.emit('knowledge:cache-cleared');
    }

    /**
     * Get cached knowledge entry
     * @param {number} entryId - Entry ID
     * @returns {Object|null} - Cached entry or null
     */
    getCachedEntry(entryId) {
        return this.knowledgeEntries.get(entryId) || null;
    }

    /**
     * Get all cached entries
     * @returns {Array} - Array of cached entries
     */
    getCachedEntries() {
        return Array.from(this.knowledgeEntries.values());
    }

    /**
     * Validate knowledge entry data
     * @param {Object} entryData - Entry data to validate
     * @returns {Object} - Validation result
     */
    validateEntryData(entryData) {
        const errors = [];

        if (!entryData.title || entryData.title.trim().length === 0) {
            errors.push('Title is required');
        }

        if (!entryData.content || entryData.content.trim().length === 0) {
            errors.push('Content is required');
        }

        if (entryData.title && entryData.title.length > 255) {
            errors.push('Title must be less than 255 characters');
        }

        if (entryData.content && entryData.content.length > 50000) {
            errors.push('Content must be less than 50,000 characters');
        }

        if (entryData.tags && !Array.isArray(entryData.tags)) {
            errors.push('Tags must be an array');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Format knowledge entry for display
     * @param {Object} entry - Knowledge entry
     * @returns {Object} - Formatted entry
     */
    formatEntryForDisplay(entry) {
        return {
            ...entry,
            formattedCreatedAt: this.formatDate(entry.created_at),
            formattedUpdatedAt: this.formatDate(entry.updated_at),
            formattedLastUsed: entry.last_used_at ? this.formatDate(entry.last_used_at) : 'Never',
            confidencePercentage: Math.round((entry.confidence_score || 0) * 100),
            tagsString: (entry.tags || []).join(', '),
            contentPreview: this.getContentPreview(entry.content),
            sourceIcon: this.getSourceIcon(entry.source_type)
        };
    }

    /**
     * Get content preview
     * @param {string} content - Full content
     * @param {number} maxLength - Maximum preview length
     * @returns {string} - Content preview
     */
    getContentPreview(content, maxLength = 150) {
        if (!content) return '';
        return content.length > maxLength 
            ? content.substring(0, maxLength) + '...'
            : content;
    }

    /**
     * Get source icon
     * @param {string} sourceType - Source type
     * @returns {string} - Bootstrap icon class
     */
    getSourceIcon(sourceType) {
        const iconMap = {
            'conversation': 'bi-chat-dots',
            'upload': 'bi-upload',
            'import': 'bi-download',
            'manual': 'bi-pencil',
            'web': 'bi-globe',
            'api': 'bi-code'
        };
        return iconMap[sourceType] || 'bi-file';
    }

    /**
     * Format date for display
     * @param {string} dateString - ISO date string
     * @returns {string} - Formatted date
     */
    formatDate(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }

    /**
     * Get knowledge entry statistics
     * @param {Object} entry - Knowledge entry
     * @returns {Object} - Entry statistics
     */
    getEntryStats(entry) {
        return {
            wordCount: (entry.content || '').split(/\s+/).length,
            characterCount: (entry.content || '').length,
            tagCount: (entry.tags || []).length,
            usageCount: entry.usage_count || 0,
            daysSinceCreated: Math.floor((Date.now() - new Date(entry.created_at)) / 86400000),
            daysSinceLastUsed: entry.last_used_at 
                ? Math.floor((Date.now() - new Date(entry.last_used_at)) / 86400000)
                : null
        };
    }
}

// Export for use in other modules
window.KnowledgeService = KnowledgeService;
