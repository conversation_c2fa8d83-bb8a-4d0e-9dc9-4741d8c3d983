/**
 * PersonalityService - AI personality management for WIDDX AI
 * Handles personality selection, customization, and user memories
 */
class PersonalityService extends EventEmitter {
    constructor(apiClient, storageManager) {
        super();
        this.apiClient = apiClient;
        this.storage = storageManager;
        this.currentPersonality = null;
        this.availablePersonalities = [];
        this.userMemories = new Map();
        this.personalityCache = new Map();
        
        this.initializePersonality();
    }

    /**
     * Initialize personality service
     */
    async initializePersonality() {
        try {
            // Load cached personality data
            await this.loadCachedData();
            
            // Load available personalities
            await this.loadAvailablePersonalities();
            
            // Load user's current personality
            await this.loadUserPersonality();
            
            this.emit('personality:initialized');
        } catch (error) {
            console.error('Personality initialization failed:', error);
            this.emit('personality:error', error);
        }
    }

    /**
     * Load cached personality data
     */
    async loadCachedData() {
        try {
            const cachedPersonalities = this.storage.getLocal('personalities', []);
            cachedPersonalities.forEach(personality => {
                this.personalityCache.set(personality.id, personality);
            });

            const cachedMemories = this.storage.getLocal('user_memories', []);
            cachedMemories.forEach(memory => {
                this.userMemories.set(memory.key, memory);
            });
        } catch (error) {
            console.warn('Failed to load cached personality data:', error);
        }
    }

    /**
     * Load available personalities from server
     * @returns {Promise<Array>} - Array of available personalities
     */
    async loadAvailablePersonalities() {
        try {
            const response = await this.apiClient.get('/v2/personality/available');
            this.availablePersonalities = response.data.data || response.data;
            
            // Cache personalities
            this.availablePersonalities.forEach(personality => {
                this.personalityCache.set(personality.id, personality);
            });
            
            // Store in local storage
            this.storage.setLocal('personalities', this.availablePersonalities);
            
            this.emit('personality:personalities-loaded', this.availablePersonalities);
            return this.availablePersonalities;

        } catch (error) {
            console.error('Failed to load personalities:', error);
            this.emit('personality:load-error', error);
            throw error;
        }
    }

    /**
     * Load user's current personality
     * @returns {Promise<Object>} - User personality data
     */
    async loadUserPersonality() {
        try {
            const response = await this.apiClient.get('/v2/personality');
            this.currentPersonality = response.data;
            
            // Cache current personality
            this.storage.setLocal('current_personality', this.currentPersonality);
            
            this.emit('personality:current-loaded', this.currentPersonality);
            return this.currentPersonality;

        } catch (error) {
            console.error('Failed to load user personality:', error);
            this.emit('personality:load-error', error);
            throw error;
        }
    }

    /**
     * Switch to a different personality
     * @param {number} personalityId - Personality ID
     * @returns {Promise<Object>} - Updated personality data
     */
    async switchPersonality(personalityId) {
        try {
            this.emit('personality:switching', personalityId);

            const response = await this.apiClient.post('/v2/personality/switch', {
                personality_id: personalityId
            });

            this.currentPersonality = response.data;
            
            // Update cache
            this.storage.setLocal('current_personality', this.currentPersonality);
            
            this.emit('personality:switched', this.currentPersonality);
            return this.currentPersonality;

        } catch (error) {
            this.emit('personality:switch-error', error);
            throw error;
        }
    }

    /**
     * Customize personality traits
     * @param {Object} customizations - Personality customizations
     * @returns {Promise<Object>} - Updated personality data
     */
    async customizePersonality(customizations) {
        try {
            this.emit('personality:customizing');

            const response = await this.apiClient.put('/v2/personality', {
                customizations
            });

            this.currentPersonality = response.data;
            
            // Update cache
            this.storage.setLocal('current_personality', this.currentPersonality);
            
            this.emit('personality:customized', this.currentPersonality);
            return this.currentPersonality;

        } catch (error) {
            this.emit('personality:customize-error', error);
            throw error;
        }
    }

    /**
     * Load user memories
     * @param {Object} filters - Memory filters
     * @returns {Promise<Array>} - Array of user memories
     */
    async loadUserMemories(filters = {}) {
        try {
            const params = {
                type: filters.type,
                limit: filters.limit || 50,
                offset: filters.offset || 0
            };

            const response = await this.apiClient.get('/v2/personality/memories', { params });
            const memories = response.data.data || response.data;

            // Update local cache
            memories.forEach(memory => {
                this.userMemories.set(memory.key, memory);
            });

            // Cache in storage
            this.storage.setLocal('user_memories', Array.from(this.userMemories.values()));

            this.emit('personality:memories-loaded', memories);
            return memories;

        } catch (error) {
            this.emit('personality:memories-load-error', error);
            throw error;
        }
    }

    /**
     * Add or update user memory
     * @param {Object} memoryData - Memory data
     * @returns {Promise<Object>} - Created/updated memory
     */
    async addMemory(memoryData) {
        try {
            const response = await this.apiClient.post('/v2/personality/memories', memoryData);
            const memory = response.data;

            // Update local cache
            this.userMemories.set(memory.key, memory);
            this.storage.setLocal('user_memories', Array.from(this.userMemories.values()));

            this.emit('personality:memory-added', memory);
            return memory;

        } catch (error) {
            this.emit('personality:memory-add-error', error);
            throw error;
        }
    }

    /**
     * Update user memory
     * @param {string} memoryKey - Memory key
     * @param {Object} updateData - Updated memory data
     * @returns {Promise<Object>} - Updated memory
     */
    async updateMemory(memoryKey, updateData) {
        try {
            const response = await this.apiClient.put(`/v2/personality/memories/${memoryKey}`, updateData);
            const memory = response.data;

            // Update local cache
            this.userMemories.set(memory.key, memory);
            this.storage.setLocal('user_memories', Array.from(this.userMemories.values()));

            this.emit('personality:memory-updated', memory);
            return memory;

        } catch (error) {
            this.emit('personality:memory-update-error', error);
            throw error;
        }
    }

    /**
     * Delete user memory
     * @param {string} memoryType - Memory type
     * @param {string} memoryKey - Memory key
     * @returns {Promise<boolean>} - Success status
     */
    async deleteMemory(memoryType, memoryKey) {
        try {
            await this.apiClient.delete(`/v2/personality/memories/${memoryType}/${memoryKey}`);

            // Remove from local cache
            this.userMemories.delete(memoryKey);
            this.storage.setLocal('user_memories', Array.from(this.userMemories.values()));

            this.emit('personality:memory-deleted', { type: memoryType, key: memoryKey });
            return true;

        } catch (error) {
            this.emit('personality:memory-delete-error', error);
            throw error;
        }
    }

    /**
     * Get personality by ID
     * @param {number} personalityId - Personality ID
     * @returns {Object|null} - Personality data or null
     */
    getPersonality(personalityId) {
        return this.personalityCache.get(personalityId) || null;
    }

    /**
     * Get current personality
     * @returns {Object|null} - Current personality or null
     */
    getCurrentPersonality() {
        return this.currentPersonality;
    }

    /**
     * Get available personalities
     * @returns {Array} - Array of available personalities
     */
    getAvailablePersonalities() {
        return this.availablePersonalities;
    }

    /**
     * Get user memories
     * @param {string} type - Memory type filter
     * @returns {Array} - Array of user memories
     */
    getUserMemories(type = null) {
        const memories = Array.from(this.userMemories.values());
        return type ? memories.filter(memory => memory.memory_type === type) : memories;
    }

    /**
     * Search memories
     * @param {string} query - Search query
     * @param {Object} filters - Search filters
     * @returns {Array} - Filtered memories
     */
    searchMemories(query, filters = {}) {
        const memories = this.getUserMemories(filters.type);
        
        if (!query) return memories;

        const searchTerm = query.toLowerCase();
        return memories.filter(memory => {
            const searchableText = [
                memory.key,
                memory.context,
                JSON.stringify(memory.value)
            ].join(' ').toLowerCase();
            
            return searchableText.includes(searchTerm);
        });
    }

    /**
     * Get personality traits for customization
     * @returns {Object} - Personality traits configuration
     */
    getPersonalityTraits() {
        return {
            communication_style: {
                label: 'Communication Style',
                type: 'select',
                options: [
                    { value: 'formal', label: 'Formal' },
                    { value: 'casual', label: 'Casual' },
                    { value: 'friendly', label: 'Friendly' },
                    { value: 'professional', label: 'Professional' }
                ],
                default: 'friendly'
            },
            verbosity: {
                label: 'Response Length',
                type: 'range',
                min: 1,
                max: 5,
                default: 3,
                labels: ['Very Brief', 'Brief', 'Moderate', 'Detailed', 'Very Detailed']
            },
            creativity: {
                label: 'Creativity Level',
                type: 'range',
                min: 1,
                max: 5,
                default: 3,
                labels: ['Conservative', 'Cautious', 'Balanced', 'Creative', 'Very Creative']
            },
            empathy: {
                label: 'Empathy Level',
                type: 'range',
                min: 1,
                max: 5,
                default: 4,
                labels: ['Analytical', 'Logical', 'Balanced', 'Empathetic', 'Very Empathetic']
            },
            humor: {
                label: 'Humor Usage',
                type: 'range',
                min: 1,
                max: 5,
                default: 2,
                labels: ['None', 'Minimal', 'Occasional', 'Frequent', 'Constant']
            },
            expertise_focus: {
                label: 'Expertise Focus',
                type: 'multiselect',
                options: [
                    { value: 'coding', label: 'Programming & Development' },
                    { value: 'research', label: 'Research & Analysis' },
                    { value: 'creative', label: 'Creative Writing' },
                    { value: 'business', label: 'Business & Strategy' },
                    { value: 'education', label: 'Teaching & Learning' },
                    { value: 'science', label: 'Science & Technology' }
                ],
                default: ['coding', 'research']
            }
        };
    }

    /**
     * Get memory types
     * @returns {Array} - Array of memory types
     */
    getMemoryTypes() {
        return [
            { value: 'preference', label: 'User Preferences', icon: 'bi-gear' },
            { value: 'fact', label: 'Personal Facts', icon: 'bi-person' },
            { value: 'interest', label: 'Interests & Hobbies', icon: 'bi-heart' },
            { value: 'goal', label: 'Goals & Objectives', icon: 'bi-target' },
            { value: 'context', label: 'Contextual Information', icon: 'bi-info-circle' },
            { value: 'skill', label: 'Skills & Expertise', icon: 'bi-award' },
            { value: 'relationship', label: 'Relationships', icon: 'bi-people' },
            { value: 'experience', label: 'Past Experiences', icon: 'bi-clock-history' }
        ];
    }

    /**
     * Export personality data
     * @returns {Object} - Exported personality data
     */
    exportPersonalityData() {
        return {
            current_personality: this.currentPersonality,
            available_personalities: this.availablePersonalities,
            user_memories: Array.from(this.userMemories.values()),
            export_timestamp: new Date().toISOString(),
            version: '2.0.0'
        };
    }

    /**
     * Import personality data
     * @param {Object} data - Personality data to import
     * @returns {Promise<boolean>} - Success status
     */
    async importPersonalityData(data) {
        try {
            // Validate data structure
            if (!data.current_personality || !Array.isArray(data.user_memories)) {
                throw new Error('Invalid personality data format');
            }

            // Import memories
            for (const memory of data.user_memories) {
                await this.addMemory(memory);
            }

            // Switch to imported personality if different
            if (data.current_personality.id !== this.currentPersonality?.id) {
                await this.switchPersonality(data.current_personality.id);
            }

            // Apply customizations
            if (data.current_personality.customizations) {
                await this.customizePersonality(data.current_personality.customizations);
            }

            this.emit('personality:data-imported');
            return true;

        } catch (error) {
            this.emit('personality:import-error', error);
            throw error;
        }
    }

    /**
     * Reset personality to default
     * @returns {Promise<Object>} - Reset personality data
     */
    async resetPersonality() {
        try {
            const response = await this.apiClient.post('/v2/personality/reset');
            this.currentPersonality = response.data;
            
            // Clear local cache
            this.storage.removeLocal('current_personality');
            this.storage.removeLocal('user_memories');
            this.userMemories.clear();
            
            this.emit('personality:reset', this.currentPersonality);
            return this.currentPersonality;

        } catch (error) {
            this.emit('personality:reset-error', error);
            throw error;
        }
    }

    /**
     * Get personality statistics
     * @returns {Object} - Personality usage statistics
     */
    getPersonalityStats() {
        const memories = Array.from(this.userMemories.values());
        const memoryTypes = this.getMemoryTypes();
        
        const stats = {
            total_memories: memories.length,
            memory_by_type: {},
            most_reinforced: null,
            recent_memories: memories
                .sort((a, b) => new Date(b.created_at) - new Date(a.created_at))
                .slice(0, 5),
            confidence_distribution: {
                high: memories.filter(m => m.confidence >= 0.8).length,
                medium: memories.filter(m => m.confidence >= 0.5 && m.confidence < 0.8).length,
                low: memories.filter(m => m.confidence < 0.5).length
            }
        };

        // Count memories by type
        memoryTypes.forEach(type => {
            stats.memory_by_type[type.value] = memories.filter(m => m.memory_type === type.value).length;
        });

        // Find most reinforced memory
        if (memories.length > 0) {
            stats.most_reinforced = memories.reduce((max, memory) => 
                memory.reinforcement_count > (max?.reinforcement_count || 0) ? memory : max
            );
        }

        return stats;
    }
}

// Export for use in other modules
window.PersonalityService = PersonalityService;
