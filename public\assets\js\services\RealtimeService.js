/**
 * RealtimeService - Real-time communication service for WIDDX AI
 * Handles WebSocket connections, typing indicators, and live updates
 */
class RealtimeService extends EventEmitter {
    constructor(authToken) {
        super();
        this.authToken = authToken;
        this.socket = null;
        this.isConnected = false;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000; // Start with 1 second
        this.maxReconnectDelay = 30000; // Max 30 seconds
        this.heartbeatInterval = null;
        this.heartbeatTimeout = null;
        this.currentRoom = null;
        this.typingUsers = new Set();
        this.typingTimeout = null;
        
        this.initialize();
    }

    /**
     * Initialize real-time service
     */
    initialize() {
        if (typeof io === 'undefined') {
            console.warn('Socket.IO not available, real-time features disabled');
            return;
        }

        this.connect();
    }

    /**
     * Connect to WebSocket server
     */
    connect() {
        if (this.isConnecting || this.isConnected) {
            return;
        }

        this.isConnecting = true;
        this.emit('realtime:connecting');

        try {
            this.socket = io('/ws', {
                auth: {
                    token: this.authToken
                },
                transports: ['websocket', 'polling'],
                timeout: 10000,
                forceNew: true
            });

            this.setupEventListeners();

        } catch (error) {
            console.error('Failed to create socket connection:', error);
            this.handleConnectionError(error);
        }
    }

    /**
     * Set up socket event listeners
     */
    setupEventListeners() {
        if (!this.socket) return;

        // Connection events
        this.socket.on('connect', () => {
            this.handleConnect();
        });

        this.socket.on('disconnect', (reason) => {
            this.handleDisconnect(reason);
        });

        this.socket.on('connect_error', (error) => {
            this.handleConnectionError(error);
        });

        // Authentication events
        this.socket.on('authenticated', (data) => {
            this.handleAuthenticated(data);
        });

        this.socket.on('auth_error', (error) => {
            this.handleAuthError(error);
        });

        // Chat events
        this.socket.on('new_message', (message) => {
            this.handleNewMessage(message);
        });

        this.socket.on('message_updated', (message) => {
            this.handleMessageUpdated(message);
        });

        this.socket.on('message_deleted', (data) => {
            this.handleMessageDeleted(data);
        });

        // Typing events
        this.socket.on('user_typing', (data) => {
            this.handleUserTyping(data);
        });

        this.socket.on('user_stopped_typing', (data) => {
            this.handleUserStoppedTyping(data);
        });

        // Conversation events
        this.socket.on('conversation_updated', (conversation) => {
            this.handleConversationUpdated(conversation);
        });

        // System events
        this.socket.on('system_notification', (notification) => {
            this.handleSystemNotification(notification);
        });

        // Heartbeat
        this.socket.on('pong', () => {
            this.handlePong();
        });

        // Error handling
        this.socket.on('error', (error) => {
            this.handleSocketError(error);
        });
    }

    /**
     * Handle successful connection
     */
    handleConnect() {
        this.isConnected = true;
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;

        console.log('Real-time connection established');
        this.emit('realtime:connected');

        // Start heartbeat
        this.startHeartbeat();

        // Rejoin current room if any
        if (this.currentRoom) {
            this.joinRoom(this.currentRoom);
        }
    }

    /**
     * Handle disconnection
     * @param {string} reason - Disconnection reason
     */
    handleDisconnect(reason) {
        this.isConnected = false;
        this.isConnecting = false;

        console.log('Real-time connection lost:', reason);
        this.emit('realtime:disconnected', reason);

        // Stop heartbeat
        this.stopHeartbeat();

        // Clear typing users
        this.typingUsers.clear();

        // Attempt reconnection for certain reasons
        if (reason === 'io server disconnect') {
            // Server initiated disconnect, don't reconnect automatically
            return;
        }

        this.scheduleReconnect();
    }

    /**
     * Handle connection error
     * @param {Error} error - Connection error
     */
    handleConnectionError(error) {
        this.isConnecting = false;
        console.error('Real-time connection error:', error);
        this.emit('realtime:error', error);

        this.scheduleReconnect();
    }

    /**
     * Handle authentication success
     * @param {Object} data - Authentication data
     */
    handleAuthenticated(data) {
        console.log('Real-time authentication successful:', data);
        this.emit('realtime:authenticated', data);
    }

    /**
     * Handle authentication error
     * @param {Error} error - Authentication error
     */
    handleAuthError(error) {
        console.error('Real-time authentication failed:', error);
        this.emit('realtime:auth-error', error);
        
        // Don't attempt to reconnect on auth errors
        this.disconnect();
    }

    /**
     * Handle socket error
     * @param {Error} error - Socket error
     */
    handleSocketError(error) {
        console.error('Socket error:', error);
        this.emit('realtime:socket-error', error);
    }

    /**
     * Schedule reconnection attempt
     */
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
            this.emit('realtime:max-reconnect-attempts');
            return;
        }

        this.reconnectAttempts++;
        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);

        console.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            if (!this.isConnected && !this.isConnecting) {
                this.connect();
            }
        }, delay);
    }

    /**
     * Start heartbeat mechanism
     */
    startHeartbeat() {
        this.stopHeartbeat();

        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.socket) {
                this.socket.emit('ping');
                
                // Set timeout for pong response
                this.heartbeatTimeout = setTimeout(() => {
                    console.warn('Heartbeat timeout, connection may be lost');
                    this.socket.disconnect();
                }, 5000);
            }
        }, 30000); // Send ping every 30 seconds
    }

    /**
     * Stop heartbeat mechanism
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }

        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
    }

    /**
     * Handle pong response
     */
    handlePong() {
        if (this.heartbeatTimeout) {
            clearTimeout(this.heartbeatTimeout);
            this.heartbeatTimeout = null;
        }
    }

    /**
     * Join a conversation room
     * @param {number} conversationId - Conversation ID
     */
    joinRoom(conversationId) {
        if (!this.isConnected || !this.socket) {
            this.currentRoom = conversationId;
            return;
        }

        // Leave current room first
        if (this.currentRoom && this.currentRoom !== conversationId) {
            this.leaveRoom(this.currentRoom);
        }

        this.currentRoom = conversationId;
        this.socket.emit('join_conversation', conversationId);
        
        console.log(`Joined conversation room: ${conversationId}`);
        this.emit('realtime:room-joined', conversationId);
    }

    /**
     * Leave a conversation room
     * @param {number} conversationId - Conversation ID
     */
    leaveRoom(conversationId) {
        if (!this.isConnected || !this.socket) {
            return;
        }

        this.socket.emit('leave_conversation', conversationId);
        
        if (this.currentRoom === conversationId) {
            this.currentRoom = null;
        }

        console.log(`Left conversation room: ${conversationId}`);
        this.emit('realtime:room-left', conversationId);
    }

    /**
     * Send typing indicator
     * @param {number} conversationId - Conversation ID
     */
    sendTyping(conversationId) {
        if (!this.isConnected || !this.socket) {
            return;
        }

        this.socket.emit('typing', { conversation_id: conversationId });

        // Clear existing timeout
        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
        }

        // Auto-stop typing after 3 seconds
        this.typingTimeout = setTimeout(() => {
            this.sendStoppedTyping(conversationId);
        }, 3000);
    }

    /**
     * Send stopped typing indicator
     * @param {number} conversationId - Conversation ID
     */
    sendStoppedTyping(conversationId) {
        if (!this.isConnected || !this.socket) {
            return;
        }

        this.socket.emit('stopped_typing', { conversation_id: conversationId });

        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = null;
        }
    }

    /**
     * Handle new message event
     * @param {Object} message - Message object
     */
    handleNewMessage(message) {
        this.emit('realtime:new-message', message);
    }

    /**
     * Handle message updated event
     * @param {Object} message - Updated message object
     */
    handleMessageUpdated(message) {
        this.emit('realtime:message-updated', message);
    }

    /**
     * Handle message deleted event
     * @param {Object} data - Deletion data
     */
    handleMessageDeleted(data) {
        this.emit('realtime:message-deleted', data);
    }

    /**
     * Handle user typing event
     * @param {Object} data - Typing data
     */
    handleUserTyping(data) {
        this.typingUsers.add(data.user_id);
        this.emit('realtime:user-typing', data);
    }

    /**
     * Handle user stopped typing event
     * @param {Object} data - Stopped typing data
     */
    handleUserStoppedTyping(data) {
        this.typingUsers.delete(data.user_id);
        this.emit('realtime:user-stopped-typing', data);
    }

    /**
     * Handle conversation updated event
     * @param {Object} conversation - Updated conversation
     */
    handleConversationUpdated(conversation) {
        this.emit('realtime:conversation-updated', conversation);
    }

    /**
     * Handle system notification
     * @param {Object} notification - System notification
     */
    handleSystemNotification(notification) {
        this.emit('realtime:system-notification', notification);
    }

    /**
     * Disconnect from server
     */
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }

        this.isConnected = false;
        this.isConnecting = false;
        this.currentRoom = null;
        this.typingUsers.clear();

        this.stopHeartbeat();

        if (this.typingTimeout) {
            clearTimeout(this.typingTimeout);
            this.typingTimeout = null;
        }

        this.emit('realtime:disconnected', 'manual');
    }

    /**
     * Update authentication token
     * @param {string} newToken - New authentication token
     */
    updateAuthToken(newToken) {
        this.authToken = newToken;
        
        if (this.isConnected && this.socket) {
            this.socket.auth.token = newToken;
            this.socket.emit('update_auth', { token: newToken });
        }
    }

    /**
     * Get connection status
     * @returns {Object} - Connection status
     */
    getStatus() {
        return {
            isConnected: this.isConnected,
            isConnecting: this.isConnecting,
            reconnectAttempts: this.reconnectAttempts,
            currentRoom: this.currentRoom,
            typingUsers: Array.from(this.typingUsers)
        };
    }

    /**
     * Check if user is typing
     * @param {number} userId - User ID
     * @returns {boolean} - Whether user is typing
     */
    isUserTyping(userId) {
        return this.typingUsers.has(userId);
    }

    /**
     * Get typing users count
     * @returns {number} - Number of typing users
     */
    getTypingUsersCount() {
        return this.typingUsers.size;
    }

    /**
     * Force reconnection
     */
    forceReconnect() {
        this.reconnectAttempts = 0;
        this.disconnect();
        setTimeout(() => {
            this.connect();
        }, 1000);
    }
}

// Export for use in other modules
window.RealtimeService = RealtimeService;
