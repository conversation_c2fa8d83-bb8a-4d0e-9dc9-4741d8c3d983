<!DOCTYPE html>
<html lang="en" dir="ltr" data-bs-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="WIDDX AI - Next-Generation Intelligent Assistant">
    <meta name="author" content="WIDDX AI Team">
    <title>WIDDX AI - Intelligent Assistant</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="assets/css/main.css" rel="stylesheet">
    <!-- Highlight.js for code syntax highlighting -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/github-dark.min.css" rel="stylesheet">
</head>
<body class="bg-dark text-light">
    <!-- Loading Screen -->
    <div id="loading-screen" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark" style="z-index: 9999;">
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading...</span>
            </div>
            <h4 class="text-primary">WIDDX AI</h4>
            <p class="text-muted">Initializing Intelligent Assistant...</p>
        </div>
    </div>

    <!-- Main Application Container -->
    <div id="app" class="d-none">
        <!-- Navigation Header -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark border-bottom border-secondary">
            <div class="container-fluid">
                <a class="navbar-brand d-flex align-items-center" href="#">
                    <i class="bi bi-robot text-primary me-2 fs-3"></i>
                    <span class="fw-bold">WIDDX AI</span>
                    <span class="badge bg-primary ms-2">v2.0</span>
                </a>

                <div class="d-flex align-items-center">
                    <!-- Theme Toggle -->
                    <button id="theme-toggle" class="btn btn-outline-secondary me-2" type="button">
                        <i class="bi bi-sun-fill"></i>
                    </button>

                    <!-- Language Selector -->
                    <div class="dropdown me-2">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-translate me-1"></i>
                            <span id="current-language">EN</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" data-lang="en">🇺🇸 English</a></li>
                            <li><a class="dropdown-item" href="#" data-lang="ar">🇸🇦 العربية</a></li>
                            <li><a class="dropdown-item" href="#" data-lang="fr">🇫🇷 Français</a></li>
                            <li><a class="dropdown-item" href="#" data-lang="es">🇪🇸 Español</a></li>
                            <li><a class="dropdown-item" href="#" data-lang="de">🇩🇪 Deutsch</a></li>
                        </ul>
                    </div>

                    <!-- User Menu -->
                    <div class="dropdown">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>
                            <span id="user-name">User</span>
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" data-action="personality"><i class="bi bi-person-gear me-2"></i>Personality</a></li>
                            <li><a class="dropdown-item" href="#" data-action="settings"><i class="bi bi-gear me-2"></i>Settings</a></li>
                            <li><a class="dropdown-item" href="#" data-action="knowledge"><i class="bi bi-book me-2"></i>Knowledge Base</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" data-action="admin"><i class="bi bi-shield-check me-2"></i>Admin Panel</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" data-action="logout"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content Area -->
        <div class="container-fluid p-0">
            <div class="row g-0">
                <!-- Sidebar - Conversations -->
                <div class="col-lg-3 col-xl-2 border-end border-secondary bg-dark" id="sidebar">
                    <div class="d-flex flex-column h-100">
                        <!-- New Conversation Button -->
                        <div class="p-3 border-bottom border-secondary">
                            <button id="new-conversation" class="btn btn-primary w-100">
                                <i class="bi bi-plus-circle me-2"></i>
                                <span data-i18n="chat.newConversation">New Conversation</span>
                            </button>
                        </div>

                        <!-- Conversations List -->
                        <div class="flex-grow-1 overflow-auto" id="conversations-container">
                            <div class="p-3">
                                <h6 class="text-muted mb-3" data-i18n="chat.recentConversations">Recent Conversations</h6>
                                <div id="conversations-list">
                                    <!-- Conversations will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar Footer -->
                        <div class="p-3 border-top border-secondary">
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="bi bi-circle-fill text-success me-1" style="font-size: 0.5rem;"></i>
                                    <span data-i18n="status.online">Online</span>
                                </small>
                                <button class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#helpModal">
                                    <i class="bi bi-question-circle"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Chat Area -->
                <div class="col-lg-9 col-xl-10 d-flex flex-column" style="height: calc(100vh - 56px);">
                    <!-- Chat Header -->
                    <div class="border-bottom border-secondary p-3 bg-dark">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="mb-1" id="conversation-title">WIDDX AI Assistant</h5>
                                <small class="text-muted">
                                    <span id="ai-status">Ready to help</span>
                                    <span id="typing-indicator" class="d-none">
                                        <i class="bi bi-three-dots text-primary"></i>
                                        AI is thinking...
                                    </span>
                                </small>
                            </div>
                            <div class="d-flex gap-2">
                                <button class="btn btn-sm btn-outline-secondary" id="toggle-reasoning" title="Toggle Reasoning">
                                    <i class="bi bi-lightbulb"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" id="export-conversation" title="Export Conversation">
                                    <i class="bi bi-download"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-secondary" id="clear-conversation" title="Clear Conversation">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Messages Container -->
                    <div class="flex-grow-1 overflow-auto p-3" id="messages-container">
                        <div id="messages-list">
                            <!-- Welcome Message -->
                            <div class="message-bubble ai-message mb-3">
                                <div class="d-flex align-items-start">
                                    <div class="avatar bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                        <i class="bi bi-robot text-white"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="bg-secondary rounded-3 p-3">
                                            <p class="mb-0" data-i18n="chat.welcomeMessage">
                                                Welcome to WIDDX AI! I'm your intelligent assistant powered by multiple AI models.
                                                I can help you with coding, research, creative writing, and much more.
                                                How can I assist you today?
                                            </p>
                                        </div>
                                        <small class="text-muted mt-1 d-block">
                                            <i class="bi bi-clock me-1"></i>
                                            <span class="timestamp">Just now</span>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message Input Area -->
                    <div class="border-top border-secondary p-3 bg-dark">
                        <form id="message-form" class="d-flex gap-2">
                            <div class="flex-grow-1 position-relative">
                                <textarea
                                    id="message-input"
                                    class="form-control bg-dark text-light border-secondary"
                                    placeholder="Type your message here..."
                                    rows="1"
                                    style="resize: none; max-height: 120px;"
                                    data-i18n-placeholder="chat.messagePlaceholder"
                                ></textarea>

                                <!-- File Upload Button -->
                                <button type="button" class="btn btn-sm btn-outline-secondary position-absolute"
                                        style="top: 8px; right: 8px;" id="file-upload-btn">
                                    <i class="bi bi-paperclip"></i>
                                </button>
                                <input type="file" id="file-input" class="d-none" multiple
                                       accept=".pdf,.docx,.txt,.jpg,.jpeg,.png,.gif,.mp3,.wav,.csv,.json">
                            </div>

                            <div class="d-flex flex-column gap-1">
                                <button type="submit" class="btn btn-primary" id="send-button">
                                    <i class="bi bi-send"></i>
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" id="voice-input-btn">
                                    <i class="bi bi-mic"></i>
                                </button>
                            </div>
                        </form>

                        <!-- Attached Files Preview -->
                        <div id="attached-files" class="mt-2 d-none">
                            <div class="d-flex flex-wrap gap-2" id="files-preview"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" id="toast-container"></div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/marked/11.1.1/marked.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.4/socket.io.js"></script>

    <!-- Configuration -->
    <script src="assets/js/config.js"></script>

    <!-- Core Application Scripts -->
    <script src="assets/js/core/EventEmitter.js"></script>
    <script src="assets/js/core/ApiClient.js"></script>
    <script src="assets/js/core/StorageManager.js"></script>
    <script src="assets/js/core/I18nManager.js"></script>
    <script src="assets/js/core/ThemeManager.js"></script>

    <!-- Service Classes -->
    <script src="assets/js/services/AuthService.js"></script>
    <script src="assets/js/services/ChatService.js"></script>
    <script src="assets/js/services/PersonalityService.js"></script>
    <script src="assets/js/services/KnowledgeService.js"></script>
    <script src="assets/js/services/FileService.js"></script>
    <script src="assets/js/services/RealtimeService.js"></script>

    <!-- UI Components -->
    <script src="assets/js/components/MessageRenderer.js"></script>
    <script src="assets/js/components/ConversationList.js"></script>
    <script src="assets/js/components/ChatInterface.js"></script>
    <script src="assets/js/components/ToastManager.js"></script>

    <!-- Main Application -->
    <script src="assets/js/WiddxApp.js"></script>

    <script>
        // Initialize the application
        document.addEventListener('DOMContentLoaded', () => {
            window.widdxApp = new WiddxApp();
        });
    </script>
</body>
</html>
